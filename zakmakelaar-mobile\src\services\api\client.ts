import {
  ApiResponse,
  User,
  Listing,
  Application,
  SearchFilters,
  PaginationInfo,
} from "@/types";
import * as SecureStore from "expo-secure-store";
import { getApiBaseUrl as getConfiguredApiBaseUrl } from "@/config/api";

// Configuration - now using centralized config
const API_BASE_URL = getConfiguredApiBaseUrl();

const TOKEN_KEY = "auth_token";

class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string = API_BASE_URL) {
    this.baseURL = baseURL;
    this.initializeToken();
  }

  private async initializeToken() {
    try {
      this.token = await SecureStore.getItemAsync(TOKEN_KEY);
    } catch (error) {
      console.error("Failed to load auth token:", error);
    }
  }

  private async getAuthHeaders(): Promise<Record<string, string>> {
    const headers: Record<string, string> = {
      "Content-Type": "application/json",
    };

    if (!this.token) {
      this.token = await SecureStore.getItemAsync(TOKEN_KEY);
    }

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    return headers;
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<ApiResponse<T>> {
    try {
      const url = `${this.baseURL}${endpoint}`;
      const headers = await this.getAuthHeaders();

      console.log(`Making API request to: ${url}`);

      const response = await fetch(url, {
        ...options,
        headers: {
          ...headers,
          ...options.headers,
        },
      });

      if (!response.ok) {
        if (response.status === 401) {
          // Token expired or invalid
          await this.clearToken();
          throw new Error("Authentication required");
        }

        let errorMessage = `HTTP ${response.status}: ${response.statusText}`;
        try {
          const errorData = await response.json();
          if (errorData.message) {
            errorMessage = errorData.message;
          }
        } catch (e) {
          // Ignore JSON parsing errors for error responses
        }

        throw new Error(errorMessage);
      }

      const data = await response.json();
      return data;
    } catch (error) {
      console.error(`API request failed for ${endpoint}:`, error);

      // Provide more helpful error messages
      if (
        error instanceof TypeError &&
        error.message.includes("Network request failed")
      ) {
        throw new Error(
          `Cannot connect to server at ${this.baseURL}. Make sure the backend is running and accessible.`
        );
      }

      throw error;
    }
  }

  // Authentication methods
  async login(
    email: string,
    password: string
  ): Promise<ApiResponse<{ user: User; token: string }>> {
    const response = await this.request<{ user: User; token: string }>(
      "/auth/login",
      {
        method: "POST",
        body: JSON.stringify({ email, password }),
      }
    );

    if (response.status === "success" && response.data?.token) {
      await this.setToken(response.data.token);
    }

    return response;
  }

  async register(
    email: string,
    password: string
  ): Promise<ApiResponse<{ user: User; token: string }>> {
    const response = await this.request<{ user: User; token: string }>(
      "/auth/register",
      {
        method: "POST",
        body: JSON.stringify({ email, password }),
      }
    );

    if (response.status === "success" && response.data?.token) {
      await this.setToken(response.data.token);
    }

    return response;
  }

  async logout(): Promise<void> {
    await this.clearToken();
  }

  private async setToken(token: string): Promise<void> {
    this.token = token;
    await SecureStore.setItemAsync(TOKEN_KEY, token);
  }

  private async clearToken(): Promise<void> {
    this.token = null;
    await SecureStore.deleteItemAsync(TOKEN_KEY);
  }

  // User methods
  async getCurrentUser(): Promise<ApiResponse<User>> {
    return this.request<User>("/auth/me");
  }

  async updateUserPreferences(
    preferences: Partial<User["preferences"]>
  ): Promise<ApiResponse<User>> {
    return this.request<User>("/auth/preferences", {
      method: "PUT",
      body: JSON.stringify({ preferences }),
    });
  }

  async updateUserProfile(
    profile: Partial<User["profile"]>
  ): Promise<ApiResponse<User>> {
    return this.request<User>("/auth/profile", {
      method: "PUT",
      body: JSON.stringify({ profile }),
    });
  }

  // Listings methods
  async getListings(
    params: {
      page?: number;
      limit?: number;
      sortBy?: string;
      sortOrder?: "asc" | "desc";
      filters?: SearchFilters;
    } = {}
  ): Promise<ApiResponse<{ listings: Listing[]; pagination: PaginationInfo }>> {
    const searchParams = new URLSearchParams();

    if (params.page) searchParams.append("page", params.page.toString());
    if (params.limit) searchParams.append("limit", params.limit.toString());
    if (params.sortBy) searchParams.append("sortBy", params.sortBy);
    if (params.sortOrder) searchParams.append("sortOrder", params.sortOrder);

    // Add filter parameters
    if (params.filters) {
      Object.entries(params.filters).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          if (Array.isArray(value)) {
            value.forEach((v) => searchParams.append(key, v.toString()));
          } else {
            searchParams.append(key, value.toString());
          }
        }
      });
    }

    const queryString = searchParams.toString();
    const endpoint = `/listings${queryString ? `?${queryString}` : ""}`;

    return this.request<{ listings: Listing[]; pagination: PaginationInfo }>(
      endpoint
    );
  }

  async getListing(id: string): Promise<ApiResponse<Listing>> {
    return this.request<Listing>(`/listings/${id}`);
  }

  async searchListings(
    query: string
  ): Promise<ApiResponse<{ listings: Listing[] }>> {
    return this.request<{ listings: Listing[] }>(
      `/search?q=${encodeURIComponent(query)}`
    );
  }

  async getSearchSuggestions(
    query: string,
    type: "location" | "propertyType" = "location"
  ) {
    return this.request(
      `/search/suggestions?q=${encodeURIComponent(query)}&type=${type}`
    );
  }

  // Applications methods
  async getApplications(): Promise<ApiResponse<Application[]>> {
    return this.request<Application[]>("/applications");
  }

  async createApplication(
    listingId: string
  ): Promise<ApiResponse<Application>> {
    return this.request<Application>("/applications", {
      method: "POST",
      body: JSON.stringify({ listingId }),
    });
  }

  async updateApplication(
    id: string,
    updates: Partial<Application>
  ): Promise<ApiResponse<Application>> {
    return this.request<Application>(`/applications/${id}`, {
      method: "PUT",
      body: JSON.stringify(updates),
    });
  }

  // Scraper methods
  async triggerScrape(): Promise<
    ApiResponse<{ message: string; newListings: number }>
  > {
    return this.request<{ message: string; newListings: number }>("/scrape", {
      method: "POST",
    });
  }

  async getScrapingStatus(): Promise<
    ApiResponse<{ isRunning: boolean; lastRun: string; stats: any }>
  > {
    return this.request<{ isRunning: boolean; lastRun: string; stats: any }>(
      "/scrape/status"
    );
  }

  // Health check
  async healthCheck(): Promise<
    ApiResponse<{ status: string; uptime: number; services: any }>
  > {
    return this.request<{ status: string; uptime: number; services: any }>(
      "/health"
    );
  }

  // Agent-specific methods (to be implemented as backend expands)
  async getAgentStatus(): Promise<ApiResponse<any>> {
    // Placeholder for future agent status endpoint
    return this.request<any>("/agent/status");
  }

  async updateAgentConfiguration(config: any): Promise<ApiResponse<any>> {
    // Placeholder for future agent configuration endpoint
    return this.request<any>("/agent/config", {
      method: "PUT",
      body: JSON.stringify(config),
    });
  }
}

// Export singleton instance
export const apiClient = new ApiClient();
export default apiClient;
