{"version": 3, "sources": ["GestureComponents.tsx"], "names": ["RefreshControl", "RNRefreshControl", "disallowInterruption", "shouldCancelWhenOutside", "GHScrollView", "RNScrollView", "ScrollView", "React", "forwardRef", "props", "ref", "refreshControlGestureRef", "useRef", "refreshControl", "waitFor", "rest", "cloneElement", "undefined", "Switch", "RNSwitch", "shouldActivateOnStart", "TextInput", "RNTextInput", "DrawerLayoutAndroid", "RNDrawerLayoutAndroid", "FlatList", "flatListProps", "scrollViewProps", "propName", "value", "Object", "entries", "nativeViewProps", "includes", "scrollProps"], "mappings": ";;;;;;;AAAA;;AAOA;;AAcA;;AAEA;;AAKA;;;;;;;;;;AAEO,MAAMA,cAAc,GAAG,kCAAoBC,2BAApB,EAAsC;AAClEC,EAAAA,oBAAoB,EAAE,IAD4C;AAElEC,EAAAA,uBAAuB,EAAE;AAFyC,CAAtC,CAAvB,C,CAIP;;;AAGA,MAAMC,YAAY,GAAG,kCACnBC,uBADmB,EAEnB;AACEH,EAAAA,oBAAoB,EAAE,IADxB;AAEEC,EAAAA,uBAAuB,EAAE;AAF3B,CAFmB,CAArB;AAOO,MAAMG,UAAU,gBAAGC,KAAK,CAACC,UAAN,CAGxB,CAACC,KAAD,EAAQC,GAAR,KAAgB;AAChB,QAAMC,wBAAwB,GAAGJ,KAAK,CAACK,MAAN,CAA6B,IAA7B,CAAjC;AACA,QAAM;AAAEC,IAAAA,cAAF;AAAkBC,IAAAA,OAAlB;AAA2B,OAAGC;AAA9B,MAAuCN,KAA7C;AAEA,sBACE,oBAAC,YAAD,eACMM,IADN;AAEE;AACA,IAAA,GAAG,EAAEL,GAHP;AAIE,IAAA,OAAO,EAAE,CAAC,GAAG,oBAAQI,OAAR,aAAQA,OAAR,cAAQA,OAAR,GAAmB,EAAnB,CAAJ,EAA4BH,wBAA5B,CAJX,CAKE;AALF;AAME,IAAA,cAAc,EACZE,cAAc,gBACVN,KAAK,CAACS,YAAN,CAAmBH,cAAnB,EAAmC;AACjC;AACAH,MAAAA,GAAG,EAAEC;AAF4B,KAAnC,CADU,GAKVM;AAZR,KADF;AAiBD,CAxByB,CAAnB,C,CAyBP;AACA;AACA;;;AAGO,MAAMC,MAAM,GAAG,kCAAmCC,mBAAnC,EAA6C;AACjEhB,EAAAA,uBAAuB,EAAE,KADwC;AAEjEiB,EAAAA,qBAAqB,EAAE,IAF0C;AAGjElB,EAAAA,oBAAoB,EAAE;AAH2C,CAA7C,CAAf,C,CAKP;;;AAGO,MAAMmB,SAAS,GAAG,kCAAsCC,sBAAtC,CAAlB,C,CACP;;;AAGO,MAAMC,mBAAmB,GAAG,kCAEjCC,gCAFiC,EAEV;AAAEtB,EAAAA,oBAAoB,EAAE;AAAxB,CAFU,CAA5B,C,CAGP;;;AAIO,MAAMuB,QAAQ,gBAAGlB,KAAK,CAACC,UAAN,CAAiB,CAACC,KAAD,EAAQC,GAAR,KAAgB;AACvD,QAAMC,wBAAwB,GAAGJ,KAAK,CAACK,MAAN,CAA6B,IAA7B,CAAjC;AAEA,QAAM;AAAEE,IAAAA,OAAF;AAAWD,IAAAA,cAAX;AAA2B,OAAGE;AAA9B,MAAuCN,KAA7C;AAEA,QAAMiB,aAAa,GAAG,EAAtB;AACA,QAAMC,eAAe,GAAG,EAAxB;;AACA,OAAK,MAAM,CAACC,QAAD,EAAWC,KAAX,CAAX,IAAgCC,MAAM,CAACC,OAAP,CAAehB,IAAf,CAAhC,EAAsD;AACpD;AACA,QAAKiB,yCAAD,CAAuCC,QAAvC,CAAgDL,QAAhD,CAAJ,EAA+D;AAC7D;AACA;AACAD,MAAAA,eAAe,CAACC,QAAD,CAAf,GAA4BC,KAA5B;AACD,KAJD,MAIO;AACL;AACA;AACAH,MAAAA,aAAa,CAACE,QAAD,CAAb,GAA0BC,KAA1B;AACD;AACF;;AAED;AAAA;AACE;AACA,wBAAC,qBAAD;AACE,MAAA,GAAG,EAAEnB;AADP,OAEMgB,aAFN;AAGE,MAAA,qBAAqB,EAAGQ,WAAD,iBACrB,oBAAC,UAAD,eAEOA,WAFP,EAGOP,eAHP;AAIIb,QAAAA,OAAO,EAAE,CAAC,GAAG,oBAAQA,OAAR,aAAQA,OAAR,cAAQA,OAAR,GAAmB,EAAnB,CAAJ,EAA4BH,wBAA5B;AAJb,SAJJ,CAYE;AAZF;AAaE,MAAA,cAAc,EACZE,cAAc,gBACVN,KAAK,CAACS,YAAN,CAAmBH,cAAnB,EAAmC;AACjC;AACAH,QAAAA,GAAG,EAAEC;AAF4B,OAAnC,CADU,GAKVM;AAnBR;AAFF;AAyBD,CA7CuB,CAAjB,C,CAqDP", "sourcesContent": ["import * as React from 'react';\nimport {\n  PropsWithChildren,\n  ForwardedRef,\n  RefAttributes,\n  ReactElement,\n} from 'react';\nimport {\n  ScrollView as RNScrollView,\n  ScrollViewProps as RNScrollViewProps,\n  Switch as RNSwitch,\n  SwitchProps as RNSwitchProps,\n  TextInput as RNTextInput,\n  TextInputProps as RNTextInputProps,\n  DrawerLayoutAndroid as RNDrawerLayoutAndroid,\n  DrawerLayoutAndroidProps as RNDrawerLayoutAndroidProps,\n  FlatList as RNFlatList,\n  FlatListProps as RNFlatListProps,\n  RefreshControl as RNRefreshControl,\n} from 'react-native';\n\nimport createNativeWrapper from '../handlers/createNativeWrapper';\n\nimport {\n  NativeViewGestureHandlerProps,\n  nativeViewProps,\n} from '../handlers/NativeViewGestureHandler';\n\nimport { toArray } from '../utils';\n\nexport const RefreshControl = createNativeWrapper(RNRefreshControl, {\n  disallowInterruption: true,\n  shouldCancelWhenOutside: false,\n});\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport type RefreshControl = typeof RefreshControl & RNRefreshControl;\n\nconst GHScrollView = createNativeWrapper<PropsWithChildren<RNScrollViewProps>>(\n  RNScrollView,\n  {\n    disallowInterruption: true,\n    shouldCancelWhenOutside: false,\n  }\n);\nexport const ScrollView = React.forwardRef<\n  RNScrollView,\n  RNScrollViewProps & NativeViewGestureHandlerProps\n>((props, ref) => {\n  const refreshControlGestureRef = React.useRef<RefreshControl>(null);\n  const { refreshControl, waitFor, ...rest } = props;\n\n  return (\n    <GHScrollView\n      {...rest}\n      // @ts-ignore `ref` exists on `GHScrollView`\n      ref={ref}\n      waitFor={[...toArray(waitFor ?? []), refreshControlGestureRef]}\n      // @ts-ignore we don't pass `refreshing` prop as we only want to override the ref\n      refreshControl={\n        refreshControl\n          ? React.cloneElement(refreshControl, {\n              // @ts-ignore for reasons unknown to me, `ref` doesn't exist on the type inferred by TS\n              ref: refreshControlGestureRef,\n            })\n          : undefined\n      }\n    />\n  );\n});\n// Backward type compatibility with https://github.com/software-mansion/react-native-gesture-handler/blob/db78d3ca7d48e8ba57482d3fe9b0a15aa79d9932/react-native-gesture-handler.d.ts#L440-L457\n// include methods of wrapped components by creating an intersection type with the RN component instead of duplicating them.\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport type ScrollView = typeof GHScrollView & RNScrollView;\n\nexport const Switch = createNativeWrapper<RNSwitchProps>(RNSwitch, {\n  shouldCancelWhenOutside: false,\n  shouldActivateOnStart: true,\n  disallowInterruption: true,\n});\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport type Switch = typeof Switch & RNSwitch;\n\nexport const TextInput = createNativeWrapper<RNTextInputProps>(RNTextInput);\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport type TextInput = typeof TextInput & RNTextInput;\n\nexport const DrawerLayoutAndroid = createNativeWrapper<\n  PropsWithChildren<RNDrawerLayoutAndroidProps>\n>(RNDrawerLayoutAndroid, { disallowInterruption: true });\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport type DrawerLayoutAndroid = typeof DrawerLayoutAndroid &\n  RNDrawerLayoutAndroid;\n\nexport const FlatList = React.forwardRef((props, ref) => {\n  const refreshControlGestureRef = React.useRef<RefreshControl>(null);\n\n  const { waitFor, refreshControl, ...rest } = props;\n\n  const flatListProps = {};\n  const scrollViewProps = {};\n  for (const [propName, value] of Object.entries(rest)) {\n    // https://github.com/microsoft/TypeScript/issues/26255\n    if ((nativeViewProps as readonly string[]).includes(propName)) {\n      // @ts-ignore - this function cannot have generic type so we have to ignore this error\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n      scrollViewProps[propName] = value;\n    } else {\n      // @ts-ignore - this function cannot have generic type so we have to ignore this error\n      // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n      flatListProps[propName] = value;\n    }\n  }\n\n  return (\n    // @ts-ignore - this function cannot have generic type so we have to ignore this error\n    <RNFlatList\n      ref={ref}\n      {...flatListProps}\n      renderScrollComponent={(scrollProps) => (\n        <ScrollView\n          {...{\n            ...scrollProps,\n            ...scrollViewProps,\n            waitFor: [...toArray(waitFor ?? []), refreshControlGestureRef],\n          }}\n        />\n      )}\n      // @ts-ignore we don't pass `refreshing` prop as we only want to override the ref\n      refreshControl={\n        refreshControl\n          ? React.cloneElement(refreshControl, {\n              // @ts-ignore for reasons unknown to me, `ref` doesn't exist on the type inferred by TS\n              ref: refreshControlGestureRef,\n            })\n          : undefined\n      }\n    />\n  );\n}) as <ItemT = any>(\n  props: PropsWithChildren<\n    Omit<RNFlatListProps<ItemT>, 'renderScrollComponent'> &\n      RefAttributes<FlatList<ItemT>> &\n      NativeViewGestureHandlerProps\n  >,\n  ref?: ForwardedRef<FlatList<ItemT>>\n) => ReactElement | null;\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport type FlatList<ItemT = any> = typeof FlatList & RNFlatList<ItemT>;\n"]}