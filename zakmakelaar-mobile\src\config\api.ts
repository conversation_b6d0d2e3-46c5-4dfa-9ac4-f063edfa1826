// API Configuration
// This file contains the API configuration for the mobile app

/**
 * Get your computer's IP address:
 * - Windows: Run `ipconfig` in Command Prompt, look for "IPv4 Address"
 * - Mac/Linux: Run `ifconfig` in Terminal, look for "inet" under your network interface
 * - Alternative: Check your router's admin panel or network settings
 */

// Replace this with your actual IP address
const DEVELOPMENT_IP = '************';
const DEVELOPMENT_PORT = 3000;

export const API_CONFIG = {
  // Development configuration
  development: {
    baseUrl: `http://${DEVELOPMENT_IP}:${DEVELOPMENT_PORT}/api`,
    healthUrl: `http://${DEVELOPMENT_IP}:${DEVELOPMENT_PORT}/health`,
    docsUrl: `http://${DEVELOPMENT_IP}:${DEVELOPMENT_PORT}/api-docs`,
  },
  
  // Production configuration
  production: {
    baseUrl: 'https://your-production-api.com/api',
    healthUrl: 'https://your-production-api.com/health',
    docsUrl: 'https://your-production-api.com/api-docs',
  },
  
  // Request configuration
  request: {
    timeout: 10000, // 10 seconds
    retries: 2,
  },
};

// Get the current environment configuration
export const getCurrentConfig = () => {
  return __DEV__ ? API_CONFIG.development : API_CONFIG.production;
};

// Helper to get the base URL with automatic IP detection
export const getApiBaseUrl = () => {
  if (!__DEV__) {
    return API_CONFIG.production.baseUrl;
  }
  
  // Try to get the IP from Expo's manifest first (works when using Expo Go)
  try {
    const Constants = require('expo-constants').default;
    if (Constants.manifest?.debuggerHost) {
      const host = Constants.manifest.debuggerHost.split(':')[0];
      return `http://${host}:${DEVELOPMENT_PORT}/api`;
    }
  } catch (error) {
    console.warn('Could not get IP from Expo manifest:', error);
  }
  
  // Fallback to configured IP
  return API_CONFIG.development.baseUrl;
};

// Network troubleshooting information
export const NETWORK_TROUBLESHOOTING = {
  commonIssues: [
    'Backend server is not running',
    'Wrong IP address configured',
    'Device not on same network as computer',
    'Firewall blocking the connection',
    'Port 3000 is blocked or in use by another service',
  ],
  
  solutions: [
    'Make sure the backend is running: npm start in zakmakelaar directory',
    'Check your IP address and update DEVELOPMENT_IP in src/config/api.ts',
    'Connect your device to the same WiFi network as your computer',
    'Temporarily disable firewall or add exception for port 3000',
    'Try a different port if 3000 is occupied',
  ],
  
  testUrls: {
    health: () => getCurrentConfig().healthUrl,
    docs: () => getCurrentConfig().docsUrl,
    api: () => getCurrentConfig().baseUrl,
  },
};
