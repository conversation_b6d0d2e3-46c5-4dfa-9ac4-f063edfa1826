{"version": 3, "sources": ["dropHandlers.ts"], "names": ["dropHandlers", "preparedGesture", "handler", "attachedGestures", "RNGestureHandlerModule", "dropGestureHandler", "handlerTag", "config", "testId", "MountRegistry", "gestureWillUnmount"], "mappings": ";;;;;;;AAAA;;AACA;;AACA;;AAEA;;;;AAEO,SAASA,YAAT,CAAsBC,eAAtB,EAA6D;AAClE,OAAK,MAAMC,OAAX,IAAsBD,eAAe,CAACE,gBAAtC,EAAwD;AACtDC,oCAAuBC,kBAAvB,CAA0CH,OAAO,CAACI,UAAlD;;AAEA,6CAAkBJ,OAAO,CAACI,UAA1B,EAAsCJ,OAAO,CAACK,MAAR,CAAeC,MAArD;;AAEAC,iCAAcC,kBAAd,CAAiCR,OAAjC;AACD;;AAED;AACD", "sourcesContent": ["import { unregisterHandler } from '../../handlersRegistry';\nimport RNGestureHandlerModule from '../../../RNGestureHandlerModule';\nimport { scheduleFlushOperations } from '../../utils';\nimport { AttachedGestureState } from './types';\nimport { MountRegistry } from '../../../mountRegistry';\n\nexport function dropHandlers(preparedGesture: AttachedGestureState) {\n  for (const handler of preparedGesture.attachedGestures) {\n    RNGestureHandlerModule.dropGestureHandler(handler.handlerTag);\n\n    unregisterHandler(handler.handlerTag, handler.config.testId);\n\n    MountRegistry.gestureWillUnmount(handler);\n  }\n\n  scheduleFlushOperations();\n}\n"]}