{"version": 3, "sources": ["VelocityTracker.ts"], "names": ["VelocityTracker", "constructor", "samples", "Circular<PERSON><PERSON>er", "historySize", "add", "event", "push", "getVelocityEstimate", "x", "y", "w", "time", "sampleCount", "index", "size", "newestSample", "get", "previousSample", "sample", "age", "delta", "Math", "abs", "horizonMilliseconds", "assumePointerMoveStoppedMilliseconds", "minSampleSize", "xSolver", "LeastSquareSolver", "xFit", "solve", "ySolver", "yFit", "xVelocity", "coefficients", "yVelocity", "velocity", "estimate", "reset", "clear"], "mappings": ";;;;;;;AACA;;AACA;;;;;;AAEe,MAAMA,eAAN,CAAsB;AAQnCC,EAAAA,WAAW,GAAG;AAAA,kEAPiC,EAOjC;;AAAA,yCANQ,EAMR;;AAAA,iDALgB,GAKhB;;AAAA,2CAJU,CAIV;;AAAA;;AACZ,SAAKC,OAAL,GAAe,IAAIC,uBAAJ,CAAiC,KAAKC,WAAtC,CAAf;AACD;;AAEMC,EAAAA,GAAG,CAACC,KAAD,EAA4B;AACpC,SAAKJ,OAAL,CAAaK,IAAb,CAAkBD,KAAlB;AACD,GAdkC,CAgBnC;AACA;AACA;AACA;AACA;AACA;;;AACQE,EAAAA,mBAAmB,GAA4B;AACrD,UAAMC,CAAC,GAAG,EAAV;AACA,UAAMC,CAAC,GAAG,EAAV;AACA,UAAMC,CAAC,GAAG,EAAV;AACA,UAAMC,IAAI,GAAG,EAAb;AAEA,QAAIC,WAAW,GAAG,CAAlB;AACA,QAAIC,KAAK,GAAG,KAAKZ,OAAL,CAAaa,IAAb,GAAoB,CAAhC;AACA,UAAMC,YAAY,GAAG,KAAKd,OAAL,CAAae,GAAb,CAAiBH,KAAjB,CAArB;;AACA,QAAI,CAACE,YAAL,EAAmB;AACjB,aAAO,IAAP;AACD;;AAED,QAAIE,cAAc,GAAGF,YAArB,CAbqD,CAerD;AACA;;AACA,WAAOH,WAAW,GAAG,KAAKX,OAAL,CAAaa,IAAlC,EAAwC;AACtC,YAAMI,MAAM,GAAG,KAAKjB,OAAL,CAAae,GAAb,CAAiBH,KAAjB,CAAf;AAEA,YAAMM,GAAG,GAAGJ,YAAY,CAACJ,IAAb,GAAoBO,MAAM,CAACP,IAAvC;AACA,YAAMS,KAAK,GAAGC,IAAI,CAACC,GAAL,CAASJ,MAAM,CAACP,IAAP,GAAcM,cAAc,CAACN,IAAtC,CAAd;AACAM,MAAAA,cAAc,GAAGC,MAAjB;;AAEA,UACEC,GAAG,GAAG,KAAKI,mBAAX,IACAH,KAAK,GAAG,KAAKI,oCAFf,EAGE;AACA;AACD;;AAEDhB,MAAAA,CAAC,CAACF,IAAF,CAAOY,MAAM,CAACV,CAAd;AACAC,MAAAA,CAAC,CAACH,IAAF,CAAOY,MAAM,CAACT,CAAd;AACAC,MAAAA,CAAC,CAACJ,IAAF,CAAO,CAAP;AACAK,MAAAA,IAAI,CAACL,IAAL,CAAU,CAACa,GAAX;AAEAP,MAAAA,WAAW;AACXC,MAAAA,KAAK;AACN;;AAED,QAAID,WAAW,IAAI,KAAKa,aAAxB,EAAuC;AACrC,YAAMC,OAAO,GAAG,IAAIC,0BAAJ,CAAsBhB,IAAtB,EAA4BH,CAA5B,EAA+BE,CAA/B,CAAhB;AACA,YAAMkB,IAAI,GAAGF,OAAO,CAACG,KAAR,CAAc,CAAd,CAAb;;AAEA,UAAID,IAAI,KAAK,IAAb,EAAmB;AACjB,cAAME,OAAO,GAAG,IAAIH,0BAAJ,CAAsBhB,IAAtB,EAA4BF,CAA5B,EAA+BC,CAA/B,CAAhB;AACA,cAAMqB,IAAI,GAAGD,OAAO,CAACD,KAAR,CAAc,CAAd,CAAb;;AAEA,YAAIE,IAAI,KAAK,IAAb,EAAmB;AACjB,gBAAMC,SAAS,GAAGJ,IAAI,CAACK,YAAL,CAAkB,CAAlB,IAAuB,IAAzC;AACA,gBAAMC,SAAS,GAAGH,IAAI,CAACE,YAAL,CAAkB,CAAlB,IAAuB,IAAzC;AAEA,iBAAO,CAACD,SAAD,EAAYE,SAAZ,CAAP;AACD;AACF;AACF;;AAED,WAAO,IAAP;AACD;;AAEkB,MAARC,QAAQ,GAAqB;AACtC,UAAMC,QAAQ,GAAG,KAAK7B,mBAAL,EAAjB;;AACA,QAAI6B,QAAQ,KAAK,IAAjB,EAAuB;AACrB,aAAOA,QAAP;AACD;;AACD,WAAO,CAAC,CAAD,EAAI,CAAJ,CAAP;AACD;;AAEMC,EAAAA,KAAK,GAAS;AACnB,SAAKpC,OAAL,CAAaqC,KAAb;AACD;;AA5FkC", "sourcesContent": ["import { AdaptedEvent } from '../interfaces';\nimport CircularBuffer from './CircularBuffer';\nimport LeastSquareSolver from './LeastSquareSolver';\n\nexport default class VelocityTracker {\n  private assumePointerMoveStoppedMilliseconds = 40;\n  private historySize = 20;\n  private horizonMilliseconds = 300;\n  private minSampleSize = 3;\n\n  private samples: CircularBuffer<AdaptedEvent>;\n\n  constructor() {\n    this.samples = new CircularBuffer<AdaptedEvent>(this.historySize);\n  }\n\n  public add(event: AdaptedEvent): void {\n    this.samples.push(event);\n  }\n\n  // Returns an estimate of the velocity of the object being tracked by the\n  // tracker given the current information available to the tracker.\n  //\n  // Information is added using [addPosition].\n  //\n  // Returns null if there is no data on which to base an estimate.\n  private getVelocityEstimate(): [number, number] | null {\n    const x = [];\n    const y = [];\n    const w = [];\n    const time = [];\n\n    let sampleCount = 0;\n    let index = this.samples.size - 1;\n    const newestSample = this.samples.get(index);\n    if (!newestSample) {\n      return null;\n    }\n\n    let previousSample = newestSample;\n\n    // Starting with the most recent PointAtTime sample, iterate backwards while\n    // the samples represent continuous motion.\n    while (sampleCount < this.samples.size) {\n      const sample = this.samples.get(index);\n\n      const age = newestSample.time - sample.time;\n      const delta = Math.abs(sample.time - previousSample.time);\n      previousSample = sample;\n\n      if (\n        age > this.horizonMilliseconds ||\n        delta > this.assumePointerMoveStoppedMilliseconds\n      ) {\n        break;\n      }\n\n      x.push(sample.x);\n      y.push(sample.y);\n      w.push(1);\n      time.push(-age);\n\n      sampleCount++;\n      index--;\n    }\n\n    if (sampleCount >= this.minSampleSize) {\n      const xSolver = new LeastSquareSolver(time, x, w);\n      const xFit = xSolver.solve(2);\n\n      if (xFit !== null) {\n        const ySolver = new LeastSquareSolver(time, y, w);\n        const yFit = ySolver.solve(2);\n\n        if (yFit !== null) {\n          const xVelocity = xFit.coefficients[1] * 1000;\n          const yVelocity = yFit.coefficients[1] * 1000;\n\n          return [xVelocity, yVelocity];\n        }\n      }\n    }\n\n    return null;\n  }\n\n  public get velocity(): [number, number] {\n    const estimate = this.getVelocityEstimate();\n    if (estimate !== null) {\n      return estimate;\n    }\n    return [0, 0];\n  }\n\n  public reset(): void {\n    this.samples.clear();\n  }\n}\n"]}