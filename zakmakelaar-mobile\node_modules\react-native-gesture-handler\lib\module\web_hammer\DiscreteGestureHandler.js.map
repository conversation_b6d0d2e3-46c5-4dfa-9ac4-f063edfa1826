{"version": 3, "sources": ["DiscreteGestureHandler.ts"], "names": ["Gesture<PERSON>andler", "TEST_MAX_IF_NOT_NAN", "DiscreteGestureHandler", "isDiscrete", "shouldEnableGestureOnSetup", "shouldFailUnderCustomCriteria", "x", "y", "deltaX", "deltaY", "maxDeltaX", "maxDeltaY", "maxDistSq", "shouldCancelWhenOutside", "isPointInView", "Math", "abs", "transformNativeEvent", "center", "rect", "view", "getBoundingClientRect", "absoluteX", "absoluteY", "left", "top", "isGestureEnabledForEvent", "minPointers", "maxPointers", "_recognizer", "pointer<PERSON><PERSON><PERSON>", "validPointerCount", "isGestureRunning", "failed", "success"], "mappings": "AAAA;;AACA;AACA,OAAOA,cAAP,MAA2B,kBAA3B;AACA,SAASC,mBAAT,QAAoC,SAApC;;AAEA,MAAeC,sBAAf,SAA8CF,cAA9C,CAA6D;AAC7C,MAAVG,UAAU,GAAG;AACf,WAAO,IAAP;AACD;;AAE6B,MAA1BC,0BAA0B,GAAG;AAC/B,WAAO,IAAP;AACD;;AAEDC,EAAAA,6BAA6B,CAC3B;AAAEC,IAAAA,CAAF;AAAKC,IAAAA,CAAL;AAAQC,IAAAA,MAAR;AAAgBC,IAAAA;AAAhB,GAD2B,EAE3B;AAAEC,IAAAA,SAAF;AAAaC,IAAAA,SAAb;AAAwBC,IAAAA,SAAxB;AAAmCC,IAAAA;AAAnC,GAF2B,EAG3B;AACA,QAAIA,uBAAJ,EAA6B;AAC3B,UAAI,CAAC,KAAKC,aAAL,CAAmB;AAAER,QAAAA,CAAF;AAAKC,QAAAA;AAAL,OAAnB,CAAL,EAAmC;AACjC,eAAO,IAAP;AACD;AACF;;AACD,WACEN,mBAAmB,CAACc,IAAI,CAACC,GAAL,CAASR,MAAT,CAAD,EAAmBE,SAAnB,CAAnB,IACAT,mBAAmB,CAACc,IAAI,CAACC,GAAL,CAASP,MAAT,CAAD,EAAmBE,SAAnB,CADnB,IAEAV,mBAAmB,CACjBc,IAAI,CAACC,GAAL,CAASP,MAAM,GAAGA,MAAT,GAAkBD,MAAM,GAAGA,MAApC,CADiB,EAEjBI,SAFiB,CAHrB;AAQD;;AAEDK,EAAAA,oBAAoB,CAAC;AAAEC,IAAAA,MAAM,EAAE;AAAEZ,MAAAA,CAAF;AAAKC,MAAAA;AAAL;AAAV,GAAD,EAA4B;AAC9C;AACA,UAAMY,IAAI,GAAG,KAAKC,IAAL,CAAWC,qBAAX,EAAb;AAEA,WAAO;AACLC,MAAAA,SAAS,EAAEhB,CADN;AAELiB,MAAAA,SAAS,EAAEhB,CAFN;AAGLD,MAAAA,CAAC,EAAEA,CAAC,GAAGa,IAAI,CAACK,IAHP;AAILjB,MAAAA,CAAC,EAAEA,CAAC,GAAGY,IAAI,CAACM;AAJP,KAAP;AAMD;;AAEDC,EAAAA,wBAAwB,CACtB;AACEC,IAAAA,WADF;AAEEC,IAAAA,WAFF;AAGElB,IAAAA,SAHF;AAIEC,IAAAA,SAJF;AAKEC,IAAAA,SALF;AAMEC,IAAAA;AANF,GADsB,EAStBgB,WATsB,EAUtB;AAAED,IAAAA,WAAW,EAAEE,aAAf;AAA8BZ,IAAAA,MAA9B;AAAsCV,IAAAA,MAAtC;AAA8CC,IAAAA;AAA9C,GAVsB,EAWtB;AACA,UAAMsB,iBAAiB,GACrBD,aAAa,IAAIH,WAAjB,IAAgCG,aAAa,IAAIF,WADnD;;AAGA,QACE,KAAKvB,6BAAL,CACE,EAAE,GAAGa,MAAL;AAAaV,MAAAA,MAAb;AAAqBC,MAAAA;AAArB,KADF,EAEE;AACEC,MAAAA,SADF;AAEEC,MAAAA,SAFF;AAGEC,MAAAA,SAHF;AAIEC,MAAAA;AAJF,KAFF,KASA;AACA;AACC,KAACkB,iBAAD,IAAsB,KAAKC,gBAZ9B,EAaE;AACA,aAAO;AAAEC,QAAAA,MAAM,EAAE;AAAV,OAAP;AACD;;AAED,WAAO;AAAEC,MAAAA,OAAO,EAAEH;AAAX,KAAP;AACD;;AAzE0D;;AA4E7D,eAAe7B,sBAAf", "sourcesContent": ["/* eslint-disable eslint-comments/no-unlimited-disable */\n/* eslint-disable */\nimport GestureHandler from './GestureHandler';\nimport { TEST_MAX_IF_NOT_NAN } from './utils';\n\nabstract class DiscreteGestureHandler extends GestureHandler {\n  get isDiscrete() {\n    return true;\n  }\n\n  get shouldEnableGestureOnSetup() {\n    return true;\n  }\n\n  shouldFailUnderCustomCriteria(\n    { x, y, deltaX, deltaY }: any,\n    { maxDeltaX, maxDeltaY, maxDistSq, shouldCancelWhenOutside }: any\n  ) {\n    if (shouldCancelWhenOutside) {\n      if (!this.isPointInView({ x, y })) {\n        return true;\n      }\n    }\n    return (\n      TEST_MAX_IF_NOT_NAN(Math.abs(deltaX), maxDeltaX) ||\n      TEST_MAX_IF_NOT_NAN(Math.abs(deltaY), maxDeltaY) ||\n      TEST_MAX_IF_NOT_NAN(\n        Math.abs(deltaY * deltaY + deltaX * deltaX),\n        maxDistSq\n      )\n    );\n  }\n\n  transformNativeEvent({ center: { x, y } }: any) {\n    // @ts-ignore FIXME(TS)\n    const rect = this.view!.getBoundingClientRect();\n\n    return {\n      absoluteX: x,\n      absoluteY: y,\n      x: x - rect.left,\n      y: y - rect.top,\n    };\n  }\n\n  isGestureEnabledForEvent(\n    {\n      minPointers,\n      maxPointers,\n      maxDeltaX,\n      maxDeltaY,\n      maxDistSq,\n      shouldCancelWhenOutside,\n    }: any,\n    _recognizer: any,\n    { maxPointers: pointerLength, center, deltaX, deltaY }: any\n  ) {\n    const validPointerCount =\n      pointerLength >= minPointers && pointerLength <= maxPointers;\n\n    if (\n      this.shouldFailUnderCustomCriteria(\n        { ...center, deltaX, deltaY },\n        {\n          maxDeltaX,\n          maxDeltaY,\n          maxDistSq,\n          shouldCancelWhenOutside,\n        }\n      ) ||\n      // A user probably won't land a multi-pointer tap on the first tick (so we cannot just cancel each time)\n      // but if the gesture is running and the user adds or subtracts another pointer then it should fail.\n      (!validPointerCount && this.isGestureRunning)\n    ) {\n      return { failed: true };\n    }\n\n    return { success: validPointerCount };\n  }\n}\n\nexport default DiscreteGestureHandler;\n"]}