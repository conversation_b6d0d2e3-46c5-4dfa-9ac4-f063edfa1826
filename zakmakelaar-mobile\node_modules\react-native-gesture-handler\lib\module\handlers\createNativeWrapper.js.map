{"version": 3, "sources": ["createNativeWrapper.tsx"], "names": ["React", "useImperativeHandle", "useRef", "NativeViewGestureHandler", "nativeViewProps", "NATIVE_WRAPPER_PROPS_FILTER", "createNativeWrapper", "Component", "config", "ComponentWrapper", "forwardRef", "props", "ref", "gestureHandlerProps", "childProps", "Object", "keys", "reduce", "res", "key", "<PERSON><PERSON><PERSON><PERSON>", "includes", "enabled", "hitSlop", "testID", "_ref", "_gestureHandlerRef", "node", "current", "handlerTag", "displayName", "render", "name"], "mappings": ";;AAAA,OAAO,KAAKA,KAAZ,MAAuB,OAAvB;AACA,SAASC,mBAAT,EAA8BC,MAA9B,QAA4C,OAA5C;AAEA,SACEC,wBADF,EAGEC,eAHF,QAIO,4BAJP;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,MAAMC,2BAA2B,GAAG,CAClC,GAAGD,eAD+B,EAElC,uBAFkC,EAGlC,6BAHkC,CAApC;AAMA,eAAe,SAASE,mBAAT,CACbC,SADa,EAEbC,MAA+C,GAAG,EAFrC,EAGb;AAAA;;AACA,QAAMC,gBAAgB,gBAAGT,KAAK,CAACU,UAAN,CAGvB,CAACC,KAAD,EAAQC,GAAR,KAAgB;AAChB;AACA,UAAM;AAAEC,MAAAA,mBAAF;AAAuBC,MAAAA;AAAvB,QAAsCC,MAAM,CAACC,IAAP,CAAYL,KAAZ,EAAmBM,MAAnB,CAC1C,CAACC,GAAD,EAAMC,GAAN,KAAc;AACZ;AACA,YAAMC,WAA8B,GAAGf,2BAAvC;;AACA,UAAIe,WAAW,CAACC,QAAZ,CAAqBF,GAArB,CAAJ,EAA+B;AAC7B;AACAD,QAAAA,GAAG,CAACL,mBAAJ,CAAwBM,GAAxB,IAA+BR,KAAK,CAACQ,GAAD,CAApC;AACD,OAHD,MAGO;AACL;AACAD,QAAAA,GAAG,CAACJ,UAAJ,CAAeK,GAAf,IAAsBR,KAAK,CAACQ,GAAD,CAA3B;AACD;;AACD,aAAOD,GAAP;AACD,KAZyC,EAa1C;AACEL,MAAAA,mBAAmB,EAAE,EAAE,GAAGL;AAAL,OADvB;AACsC;AACpCM,MAAAA,UAAU,EAAE;AACVQ,QAAAA,OAAO,EAAEX,KAAK,CAACW,OADL;AAEVC,QAAAA,OAAO,EAAEZ,KAAK,CAACY,OAFL;AAGVC,QAAAA,MAAM,EAAEb,KAAK,CAACa;AAHJ;AAFd,KAb0C,CAA5C;;AAsBA,UAAMC,IAAI,GAAGvB,MAAM,CAAyB,IAAzB,CAAnB;;AACA,UAAMwB,kBAAkB,GAAGxB,MAAM,CAAyB,IAAzB,CAAjC;;AACAD,IAAAA,mBAAmB,CACjBW,GADiB,EAEjB;AACA,UAAM;AACJ,YAAMe,IAAI,GAAGD,kBAAkB,CAACE,OAAhC,CADI,CAEJ;;AACA,UAAIH,IAAI,CAACG,OAAL,IAAgBD,IAApB,EAA0B;AACxB;AACAF,QAAAA,IAAI,CAACG,OAAL,CAAaC,UAAb,GAA0BF,IAAI,CAACE,UAA/B;AACA,eAAOJ,IAAI,CAACG,OAAZ;AACD;;AACD,aAAO,IAAP;AACD,KAZgB,EAajB,CAACH,IAAD,EAAOC,kBAAP,CAbiB,CAAnB;AAeA,wBACE,oBAAC,wBAAD,eACMb,mBADN;AAEE;AACA,MAAA,GAAG,EAAEa;AAHP,qBAIE,oBAAC,SAAD,eAAeZ,UAAf;AAA2B,MAAA,GAAG,EAAEW;AAAhC,OAJF,CADF;AAQD,GApDwB,CAAzB,CADA,CAuDA;;AACAhB,EAAAA,gBAAgB,CAACqB,WAAjB,GACE,CAAAvB,SAAS,SAAT,IAAAA,SAAS,WAAT,YAAAA,SAAS,CAAEuB,WAAX,OACA;AACAvB,EAAAA,SAFA,aAEAA,SAFA,4CAEAA,SAAS,CAAEwB,MAFX,sDAEA,kBAAmBC,IAFnB,KAGC,OAAOzB,SAAP,KAAqB,QAArB,IAAiCA,SAHlC,IAIA,kBALF;AAOA,SAAOE,gBAAP;AACD", "sourcesContent": ["import * as React from 'react';\nimport { useImperativeHandle, useRef } from 'react';\n\nimport {\n  NativeViewGestureHandler,\n  NativeViewGestureHandlerProps,\n  nativeViewProps,\n} from './NativeViewGestureHandler';\n\n/*\n * This array should consist of:\n *   - All keys in propTypes from NativeGestureHandler\n *     (and all keys in GestureHandlerPropTypes)\n *   - 'onGestureHandlerEvent'\n *   - 'onGestureHandlerStateChange'\n */\nconst NATIVE_WRAPPER_PROPS_FILTER = [\n  ...nativeViewProps,\n  'onGestureHandlerEvent',\n  'onGestureHandlerStateChange',\n] as const;\n\nexport default function createNativeWrapper<P>(\n  Component: React.ComponentType<P>,\n  config: Readonly<NativeViewGestureHandlerProps> = {}\n) {\n  const ComponentWrapper = React.forwardRef<\n    React.ComponentType<any>,\n    P & NativeViewGestureHandlerProps\n  >((props, ref) => {\n    // Filter out props that should be passed to gesture handler wrapper\n    const { gestureHandlerProps, childProps } = Object.keys(props).reduce(\n      (res, key) => {\n        // TS being overly protective with it's types, see https://github.com/microsoft/TypeScript/issues/26255#issuecomment-458013731 for more info\n        const allowedKeys: readonly string[] = NATIVE_WRAPPER_PROPS_FILTER;\n        if (allowedKeys.includes(key)) {\n          // @ts-ignore FIXME(TS)\n          res.gestureHandlerProps[key] = props[key];\n        } else {\n          // @ts-ignore FIXME(TS)\n          res.childProps[key] = props[key];\n        }\n        return res;\n      },\n      {\n        gestureHandlerProps: { ...config }, // Watch out not to modify config\n        childProps: {\n          enabled: props.enabled,\n          hitSlop: props.hitSlop,\n          testID: props.testID,\n        } as P,\n      }\n    );\n    const _ref = useRef<React.ComponentType<P>>(null);\n    const _gestureHandlerRef = useRef<React.ComponentType<P>>(null);\n    useImperativeHandle(\n      ref,\n      // @ts-ignore TODO(TS) decide how nulls work in this context\n      () => {\n        const node = _gestureHandlerRef.current;\n        // Add handlerTag for relations config\n        if (_ref.current && node) {\n          // @ts-ignore FIXME(TS) think about createHandler return type\n          _ref.current.handlerTag = node.handlerTag;\n          return _ref.current;\n        }\n        return null;\n      },\n      [_ref, _gestureHandlerRef]\n    );\n    return (\n      <NativeViewGestureHandler\n        {...gestureHandlerProps}\n        // @ts-ignore TODO(TS)\n        ref={_gestureHandlerRef}>\n        <Component {...childProps} ref={_ref} />\n      </NativeViewGestureHandler>\n    );\n  });\n\n  // eslint-disable-next-line @typescript-eslint/no-unsafe-assignment\n  ComponentWrapper.displayName =\n    Component?.displayName ||\n    // @ts-ignore if render doesn't exist it will return undefined and go further\n    Component?.render?.name ||\n    (typeof Component === 'string' && Component) ||\n    'ComponentWrapper';\n\n  return ComponentWrapper;\n}\n"]}