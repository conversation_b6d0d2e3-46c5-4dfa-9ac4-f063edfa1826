// Typography constants and styles for consistent font usage across the app

export const FontWeights = {
  thin: '100',
  extraLight: '200',
  light: '300',
  regular: '400',
  medium: '500',
  semiBold: '600',
  bold: '700',
  extraBold: '800',
  black: '900',
} as const;

export const FontSizes = {
  xs: 12,
  sm: 14,
  base: 16,
  lg: 18,
  xl: 20,
  '2xl': 24,
  '3xl': 32,
  '4xl': 40,
} as const;

export const LineHeights = {
  tight: 1.2,
  normal: 1.4,
  relaxed: 1.6,
  loose: 1.8,
} as const;

// Common text styles
export const TextStyles = {
  // Headers
  h1: {
    fontSize: FontSizes['3xl'],
    fontWeight: FontWeights.bold,
    lineHeight: FontSizes['3xl'] * LineHeights.tight,
  },
  h2: {
    fontSize: FontSizes['2xl'],
    fontWeight: FontWeights.bold,
    lineHeight: FontSizes['2xl'] * LineHeights.tight,
  },
  h3: {
    fontSize: FontSizes.xl,
    fontWeight: FontWeights.semiBold,
    lineHeight: FontSizes.xl * LineHeights.normal,
  },
  h4: {
    fontSize: FontSizes.lg,
    fontWeight: FontWeights.semiBold,
    lineHeight: FontSizes.lg * LineHeights.normal,
  },
  
  // Body text
  body: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.regular,
    lineHeight: FontSizes.base * LineHeights.normal,
  },
  bodyMedium: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.medium,
    lineHeight: FontSizes.base * LineHeights.normal,
  },
  bodySemiBold: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semiBold,
    lineHeight: FontSizes.base * LineHeights.normal,
  },
  
  // Small text
  caption: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.regular,
    lineHeight: FontSizes.sm * LineHeights.normal,
  },
  captionMedium: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    lineHeight: FontSizes.sm * LineHeights.normal,
  },
  
  // Tiny text
  overline: {
    fontSize: FontSizes.xs,
    fontWeight: FontWeights.medium,
    lineHeight: FontSizes.xs * LineHeights.normal,
    textTransform: 'uppercase' as const,
    letterSpacing: 0.5,
  },
  
  // Button text
  button: {
    fontSize: FontSizes.base,
    fontWeight: FontWeights.semiBold,
    lineHeight: FontSizes.base * LineHeights.tight,
  },
  buttonSmall: {
    fontSize: FontSizes.sm,
    fontWeight: FontWeights.medium,
    lineHeight: FontSizes.sm * LineHeights.tight,
  },
} as const;

// Usage example:
// import { TextStyles, FontWeights } from '@/styles/typography';
// 
// const styles = StyleSheet.create({
//   title: {
//     ...TextStyles.h1,
//     color: '#ffffff',
//   },
//   subtitle: {
//     ...TextStyles.body,
//     color: '#8E8E93',
//   },
// });
