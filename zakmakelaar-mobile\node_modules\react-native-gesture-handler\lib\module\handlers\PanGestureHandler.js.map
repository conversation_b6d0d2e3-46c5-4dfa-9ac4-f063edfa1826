{"version": 3, "sources": ["PanGestureHandler.ts"], "names": ["createHandler", "baseGestureHandlerProps", "panGestureHandlerProps", "panGestureHandlerCustomNativeProps", "panHandlerName", "PanGestureHandler", "name", "allowedProps", "config", "transformProps", "managePanProps", "customNativeProps", "validatePanGestureHandlerProps", "props", "Array", "isArray", "activeOffsetX", "Error", "activeOffsetY", "failOffsetX", "failOffsetY", "minDist", "transformPanGestureHandlerProps", "res", "undefined", "activeOffsetXStart", "activeOffsetXEnd", "activeOffsetYStart", "activeOffsetYEnd", "failOffsetXStart", "failOffsetXEnd", "failOffsetYStart", "failOffsetYEnd", "__DEV__"], "mappings": "AACA,OAAOA,aAAP,MAA0B,iBAA1B;AACA,SAEEC,uBAFF,QAGO,wBAHP;AAKA,OAAO,MAAMC,sBAAsB,GAAG,CACpC,eADoC,EAEpC,eAFoC,EAGpC,aAHoC,EAIpC,aAJoC,EAKpC,SALoC,EAMpC,aANoC,EAOpC,cAPoC,EAQpC,cARoC,EASpC,aAToC,EAUpC,aAVoC,EAWpC,YAXoC,EAYpC,gCAZoC,EAapC,wBAboC,CAA/B;AAgBP,OAAO,MAAMC,kCAAkC,GAAG,CAChD,oBADgD,EAEhD,kBAFgD,EAGhD,oBAHgD,EAIhD,kBAJgD,EAKhD,kBALgD,EAMhD,gBANgD,EAOhD,kBAPgD,EAQhD,gBARgD,CAA3C;AAgHP,OAAO,MAAMC,cAAc,GAAG,mBAAvB;AAEP;AACA;AACA;;AAGA;AACA;AACA;AACA;AACA,OAAO,MAAMC,iBAAiB,GAAGL,aAAa,CAG5C;AACAM,EAAAA,IAAI,EAAEF,cADN;AAEAG,EAAAA,YAAY,EAAE,CACZ,GAAGN,uBADS,EAEZ,GAAGC,sBAFS,CAFd;AAMAM,EAAAA,MAAM,EAAE,EANR;AAOAC,EAAAA,cAAc,EAAEC,cAPhB;AAQAC,EAAAA,iBAAiB,EAAER;AARnB,CAH4C,CAAvC;;AAcP,SAASS,8BAAT,CAAwCC,KAAxC,EAAuE;AACrE,MACEC,KAAK,CAACC,OAAN,CAAcF,KAAK,CAACG,aAApB,MACCH,KAAK,CAACG,aAAN,CAAoB,CAApB,IAAyB,CAAzB,IAA8BH,KAAK,CAACG,aAAN,CAAoB,CAApB,IAAyB,CADxD,CADF,EAGE;AACA,UAAM,IAAIC,KAAJ,CACH,wFADG,CAAN;AAGD;;AAED,MACEH,KAAK,CAACC,OAAN,CAAcF,KAAK,CAACK,aAApB,MACCL,KAAK,CAACK,aAAN,CAAoB,CAApB,IAAyB,CAAzB,IAA8BL,KAAK,CAACK,aAAN,CAAoB,CAApB,IAAyB,CADxD,CADF,EAGE;AACA,UAAM,IAAID,KAAJ,CACH,wFADG,CAAN;AAGD;;AAED,MACEH,KAAK,CAACC,OAAN,CAAcF,KAAK,CAACM,WAApB,MACCN,KAAK,CAACM,WAAN,CAAkB,CAAlB,IAAuB,CAAvB,IAA4BN,KAAK,CAACM,WAAN,CAAkB,CAAlB,IAAuB,CADpD,CADF,EAGE;AACA,UAAM,IAAIF,KAAJ,CACH,sFADG,CAAN;AAGD;;AAED,MACEH,KAAK,CAACC,OAAN,CAAcF,KAAK,CAACO,WAApB,MACCP,KAAK,CAACO,WAAN,CAAkB,CAAlB,IAAuB,CAAvB,IAA4BP,KAAK,CAACO,WAAN,CAAkB,CAAlB,IAAuB,CADpD,CADF,EAGE;AACA,UAAM,IAAIH,KAAJ,CACH,sFADG,CAAN;AAGD;;AAED,MAAIJ,KAAK,CAACQ,OAAN,KAAkBR,KAAK,CAACM,WAAN,IAAqBN,KAAK,CAACO,WAA7C,CAAJ,EAA+D;AAC7D,UAAM,IAAIH,KAAJ,CACH,iHADG,CAAN;AAGD;;AAED,MAAIJ,KAAK,CAACQ,OAAN,KAAkBR,KAAK,CAACG,aAAN,IAAuBH,KAAK,CAACK,aAA/C,CAAJ,EAAmE;AACjE,UAAM,IAAID,KAAJ,CACH,wEADG,CAAN;AAGD;AACF;;AAED,SAASK,+BAAT,CAAyCT,KAAzC,EAAwE;AAatE,QAAMU,GAAmC,GAAG,EAAE,GAAGV;AAAL,GAA5C;;AAEA,MAAIA,KAAK,CAACG,aAAN,KAAwBQ,SAA5B,EAAuC;AACrC,WAAOD,GAAG,CAACP,aAAX;;AACA,QAAIF,KAAK,CAACC,OAAN,CAAcF,KAAK,CAACG,aAApB,CAAJ,EAAwC;AACtCO,MAAAA,GAAG,CAACE,kBAAJ,GAAyBZ,KAAK,CAACG,aAAN,CAAoB,CAApB,CAAzB;AACAO,MAAAA,GAAG,CAACG,gBAAJ,GAAuBb,KAAK,CAACG,aAAN,CAAoB,CAApB,CAAvB;AACD,KAHD,MAGO,IAAIH,KAAK,CAACG,aAAN,GAAsB,CAA1B,EAA6B;AAClCO,MAAAA,GAAG,CAACE,kBAAJ,GAAyBZ,KAAK,CAACG,aAA/B;AACD,KAFM,MAEA;AACLO,MAAAA,GAAG,CAACG,gBAAJ,GAAuBb,KAAK,CAACG,aAA7B;AACD;AACF;;AAED,MAAIH,KAAK,CAACK,aAAN,KAAwBM,SAA5B,EAAuC;AACrC,WAAOD,GAAG,CAACL,aAAX;;AACA,QAAIJ,KAAK,CAACC,OAAN,CAAcF,KAAK,CAACK,aAApB,CAAJ,EAAwC;AACtCK,MAAAA,GAAG,CAACI,kBAAJ,GAAyBd,KAAK,CAACK,aAAN,CAAoB,CAApB,CAAzB;AACAK,MAAAA,GAAG,CAACK,gBAAJ,GAAuBf,KAAK,CAACK,aAAN,CAAoB,CAApB,CAAvB;AACD,KAHD,MAGO,IAAIL,KAAK,CAACK,aAAN,GAAsB,CAA1B,EAA6B;AAClCK,MAAAA,GAAG,CAACI,kBAAJ,GAAyBd,KAAK,CAACK,aAA/B;AACD,KAFM,MAEA;AACLK,MAAAA,GAAG,CAACK,gBAAJ,GAAuBf,KAAK,CAACK,aAA7B;AACD;AACF;;AAED,MAAIL,KAAK,CAACM,WAAN,KAAsBK,SAA1B,EAAqC;AACnC,WAAOD,GAAG,CAACJ,WAAX;;AACA,QAAIL,KAAK,CAACC,OAAN,CAAcF,KAAK,CAACM,WAApB,CAAJ,EAAsC;AACpCI,MAAAA,GAAG,CAACM,gBAAJ,GAAuBhB,KAAK,CAACM,WAAN,CAAkB,CAAlB,CAAvB;AACAI,MAAAA,GAAG,CAACO,cAAJ,GAAqBjB,KAAK,CAACM,WAAN,CAAkB,CAAlB,CAArB;AACD,KAHD,MAGO,IAAIN,KAAK,CAACM,WAAN,GAAoB,CAAxB,EAA2B;AAChCI,MAAAA,GAAG,CAACM,gBAAJ,GAAuBhB,KAAK,CAACM,WAA7B;AACD,KAFM,MAEA;AACLI,MAAAA,GAAG,CAACO,cAAJ,GAAqBjB,KAAK,CAACM,WAA3B;AACD;AACF;;AAED,MAAIN,KAAK,CAACO,WAAN,KAAsBI,SAA1B,EAAqC;AACnC,WAAOD,GAAG,CAACH,WAAX;;AACA,QAAIN,KAAK,CAACC,OAAN,CAAcF,KAAK,CAACO,WAApB,CAAJ,EAAsC;AACpCG,MAAAA,GAAG,CAACQ,gBAAJ,GAAuBlB,KAAK,CAACO,WAAN,CAAkB,CAAlB,CAAvB;AACAG,MAAAA,GAAG,CAACS,cAAJ,GAAqBnB,KAAK,CAACO,WAAN,CAAkB,CAAlB,CAArB;AACD,KAHD,MAGO,IAAIP,KAAK,CAACO,WAAN,GAAoB,CAAxB,EAA2B;AAChCG,MAAAA,GAAG,CAACQ,gBAAJ,GAAuBlB,KAAK,CAACO,WAA7B;AACD,KAFM,MAEA;AACLG,MAAAA,GAAG,CAACS,cAAJ,GAAqBnB,KAAK,CAACO,WAA3B;AACD;AACF;;AAED,SAAOG,GAAP;AACD;;AAED,OAAO,SAASb,cAAT,CAAwBG,KAAxB,EAAuD;AAC5D,MAAIoB,OAAJ,EAAa;AACXrB,IAAAA,8BAA8B,CAACC,KAAD,CAA9B;AACD;;AACD,SAAOS,+BAA+B,CAACT,KAAD,CAAtC;AACD", "sourcesContent": ["import type { PanGestureHandlerEventPayload } from './GestureHandlerEventPayload';\nimport createHandler from './createHandler';\nimport {\n  BaseGestureHandlerProps,\n  baseGestureHandlerProps,\n} from './gestureHandlerCommon';\n\nexport const panGestureHandlerProps = [\n  'activeOffsetY',\n  'activeOffsetX',\n  'failOffsetY',\n  'failOffsetX',\n  'minDist',\n  'minVelocity',\n  'minVelocityX',\n  'minVelocityY',\n  'minPointers',\n  'maxPointers',\n  'avgTouches',\n  'enableTrackpadTwoFingerGesture',\n  'activateAfterLongPress',\n] as const;\n\nexport const panGestureHandlerCustomNativeProps = [\n  'activeOffsetYStart',\n  'activeOffsetYEnd',\n  'activeOffsetXStart',\n  'activeOffsetXEnd',\n  'failOffsetYStart',\n  'failOffsetYEnd',\n  'failOffsetXStart',\n  'failOffsetXEnd',\n] as const;\n\ninterface CommonPanProperties {\n  /**\n   * Minimum distance the finger (or multiple finger) need to travel before the\n   * handler activates. Expressed in points.\n   */\n  minDist?: number;\n\n  /**\n   * Android only.\n   */\n  avgTouches?: boolean;\n\n  /**\n   * Enables two-finger gestures on supported devices, for example iPads with\n   * trackpads. If not enabled the gesture will require click + drag, with\n   * enableTrackpadTwoFingerGesture swiping with two fingers will also trigger\n   * the gesture.\n   */\n  enableTrackpadTwoFingerGesture?: boolean;\n\n  /**\n   * A number of fingers that is required to be placed before handler can\n   * activate. Should be a higher or equal to 0 integer.\n   */\n  minPointers?: number;\n\n  /**\n   * When the given number of fingers is placed on the screen and handler hasn't\n   * yet activated it will fail recognizing the gesture. Should be a higher or\n   * equal to 0 integer.\n   */\n  maxPointers?: number;\n\n  minVelocity?: number;\n  minVelocityX?: number;\n  minVelocityY?: number;\n  activateAfterLongPress?: number;\n}\n\nexport interface PanGestureConfig extends CommonPanProperties {\n  activeOffsetYStart?: number;\n  activeOffsetYEnd?: number;\n  activeOffsetXStart?: number;\n  activeOffsetXEnd?: number;\n  failOffsetYStart?: number;\n  failOffsetYEnd?: number;\n  failOffsetXStart?: number;\n  failOffsetXEnd?: number;\n}\n\n/**\n * @deprecated PanGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Pan()` instead.\n */\nexport interface PanGestureHandlerProps\n  extends BaseGestureHandlerProps<PanGestureHandlerEventPayload>,\n    CommonPanProperties {\n  /**\n   * Range along X axis (in points) where fingers travels without activation of\n   * handler. Moving outside of this range implies activation of handler. Range\n   * can be given as an array or a single number. If range is set as an array,\n   * first value must be lower or equal to 0, a the second one higher or equal\n   * to 0. If only one number `p` is given a range of `(-inf, p)` will be used\n   * if `p` is higher or equal to 0 and `(-p, inf)` otherwise.\n   */\n  activeOffsetY?:\n    | number\n    | [activeOffsetYStart: number, activeOffsetYEnd: number];\n\n  /**\n   * Range along X axis (in points) where fingers travels without activation of\n   * handler. Moving outside of this range implies activation of handler. Range\n   * can be given as an array or a single number. If range is set as an array,\n   * first value must be lower or equal to 0, a the second one higher or equal\n   * to 0. If only one number `p` is given a range of `(-inf, p)` will be used\n   * if `p` is higher or equal to 0 and `(-p, inf)` otherwise.\n   */\n  activeOffsetX?:\n    | number\n    | [activeOffsetXStart: number, activeOffsetXEnd: number];\n\n  /**\n   * When the finger moves outside this range (in points) along Y axis and\n   * handler hasn't yet activated it will fail recognizing the gesture. Range\n   * can be given as an array or a single number. If range is set as an array,\n   * first value must be lower or equal to 0, a the second one higher or equal\n   * to 0. If only one number `p` is given a range of `(-inf, p)` will be used\n   * if `p` is higher or equal to 0 and `(-p, inf)` otherwise.\n   */\n  failOffsetY?: number | [failOffsetYStart: number, failOffsetYEnd: number];\n\n  /**\n   * When the finger moves outside this range (in points) along X axis and\n   * handler hasn't yet activated it will fail recognizing the gesture. Range\n   * can be given as an array or a single number. If range is set as an array,\n   * first value must be lower or equal to 0, a the second one higher or equal\n   * to 0. If only one number `p` is given a range of `(-inf, p)` will be used\n   * if `p` is higher or equal to 0 and `(-p, inf)` otherwise.\n   */\n  failOffsetX?: number | [failOffsetXStart: number, failOffsetXEnd: number];\n}\n\nexport const panHandlerName = 'PanGestureHandler';\n\n/**\n * @deprecated PanGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Pan()` instead.\n */\nexport type PanGestureHandler = typeof PanGestureHandler;\n\n/**\n * @deprecated PanGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Pan()` instead.\n */\n// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\nexport const PanGestureHandler = createHandler<\n  PanGestureHandlerProps,\n  PanGestureHandlerEventPayload\n>({\n  name: panHandlerName,\n  allowedProps: [\n    ...baseGestureHandlerProps,\n    ...panGestureHandlerProps,\n  ] as const,\n  config: {},\n  transformProps: managePanProps,\n  customNativeProps: panGestureHandlerCustomNativeProps,\n});\n\nfunction validatePanGestureHandlerProps(props: PanGestureHandlerProps) {\n  if (\n    Array.isArray(props.activeOffsetX) &&\n    (props.activeOffsetX[0] > 0 || props.activeOffsetX[1] < 0)\n  ) {\n    throw new Error(\n      `First element of activeOffsetX should be negative, a the second one should be positive`\n    );\n  }\n\n  if (\n    Array.isArray(props.activeOffsetY) &&\n    (props.activeOffsetY[0] > 0 || props.activeOffsetY[1] < 0)\n  ) {\n    throw new Error(\n      `First element of activeOffsetY should be negative, a the second one should be positive`\n    );\n  }\n\n  if (\n    Array.isArray(props.failOffsetX) &&\n    (props.failOffsetX[0] > 0 || props.failOffsetX[1] < 0)\n  ) {\n    throw new Error(\n      `First element of failOffsetX should be negative, a the second one should be positive`\n    );\n  }\n\n  if (\n    Array.isArray(props.failOffsetY) &&\n    (props.failOffsetY[0] > 0 || props.failOffsetY[1] < 0)\n  ) {\n    throw new Error(\n      `First element of failOffsetY should be negative, a the second one should be positive`\n    );\n  }\n\n  if (props.minDist && (props.failOffsetX || props.failOffsetY)) {\n    throw new Error(\n      `It is not supported to use minDist with failOffsetX or failOffsetY, use activeOffsetX and activeOffsetY instead`\n    );\n  }\n\n  if (props.minDist && (props.activeOffsetX || props.activeOffsetY)) {\n    throw new Error(\n      `It is not supported to use minDist with activeOffsetX or activeOffsetY`\n    );\n  }\n}\n\nfunction transformPanGestureHandlerProps(props: PanGestureHandlerProps) {\n  type InternalPanGHKeys =\n    | 'activeOffsetXStart'\n    | 'activeOffsetXEnd'\n    | 'failOffsetXStart'\n    | 'failOffsetXEnd'\n    | 'activeOffsetYStart'\n    | 'activeOffsetYEnd'\n    | 'failOffsetYStart'\n    | 'failOffsetYEnd';\n  type PanGestureHandlerInternalProps = PanGestureHandlerProps &\n    Partial<Record<InternalPanGHKeys, number>>;\n\n  const res: PanGestureHandlerInternalProps = { ...props };\n\n  if (props.activeOffsetX !== undefined) {\n    delete res.activeOffsetX;\n    if (Array.isArray(props.activeOffsetX)) {\n      res.activeOffsetXStart = props.activeOffsetX[0];\n      res.activeOffsetXEnd = props.activeOffsetX[1];\n    } else if (props.activeOffsetX < 0) {\n      res.activeOffsetXStart = props.activeOffsetX;\n    } else {\n      res.activeOffsetXEnd = props.activeOffsetX;\n    }\n  }\n\n  if (props.activeOffsetY !== undefined) {\n    delete res.activeOffsetY;\n    if (Array.isArray(props.activeOffsetY)) {\n      res.activeOffsetYStart = props.activeOffsetY[0];\n      res.activeOffsetYEnd = props.activeOffsetY[1];\n    } else if (props.activeOffsetY < 0) {\n      res.activeOffsetYStart = props.activeOffsetY;\n    } else {\n      res.activeOffsetYEnd = props.activeOffsetY;\n    }\n  }\n\n  if (props.failOffsetX !== undefined) {\n    delete res.failOffsetX;\n    if (Array.isArray(props.failOffsetX)) {\n      res.failOffsetXStart = props.failOffsetX[0];\n      res.failOffsetXEnd = props.failOffsetX[1];\n    } else if (props.failOffsetX < 0) {\n      res.failOffsetXStart = props.failOffsetX;\n    } else {\n      res.failOffsetXEnd = props.failOffsetX;\n    }\n  }\n\n  if (props.failOffsetY !== undefined) {\n    delete res.failOffsetY;\n    if (Array.isArray(props.failOffsetY)) {\n      res.failOffsetYStart = props.failOffsetY[0];\n      res.failOffsetYEnd = props.failOffsetY[1];\n    } else if (props.failOffsetY < 0) {\n      res.failOffsetYStart = props.failOffsetY;\n    } else {\n      res.failOffsetYEnd = props.failOffsetY;\n    }\n  }\n\n  return res;\n}\n\nexport function managePanProps(props: PanGestureHandlerProps) {\n  if (__DEV__) {\n    validatePanGestureHandlerProps(props);\n  }\n  return transformPanGestureHandlerProps(props);\n}\n"]}