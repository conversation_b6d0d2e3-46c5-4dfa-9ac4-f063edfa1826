# Network Setup Guide

This guide helps you connect the mobile app to your local backend server.

## Quick Setup

### 1. Find Your Computer's IP Address

**Windows:**
```bash
ipconfig
```
Look for "IPv4 Address" under your active network adapter (usually WiFi or Ethernet).

**Mac/Linux:**
```bash
ifconfig
```
Look for "inet" under your network interface (usually en0 for WiFi).

**Example IP addresses:**
- `************` (most common)
- `*************`
- `********`

### 2. Update the Configuration

Edit `src/config/api.ts` and update the `DEVELOPMENT_IP` variable:

```typescript
// Replace this with your actual IP address
const DEVELOPMENT_IP = '************'; // <- Change this to your IP
```

### 3. Test the Connection

1. Make sure your backend is running:
   ```bash
   cd zakmakelaar
   npm start
   ```

2. In the mobile app, go to the login screen and tap "Test API Connection" (only visible in development mode).

## Troubleshooting

### Common Issues

1. **"Network request failed"**
   - Backend server is not running
   - Wrong IP address configured
   - Device not on same network

2. **"Connection refused"**
   - Port 3000 is blocked or in use
   - Firewall blocking the connection

3. **"Timeout"**
   - Network is slow or unstable
   - Server is overloaded

### Solutions

1. **Check Backend Status**
   ```bash
   # In zakmakelaar directory
   npm start
   ```
   Should show: "Server is running on port 3000"

2. **Verify Network Connection**
   - Both computer and mobile device must be on the same WiFi network
   - Try accessing `http://YOUR_IP:3000/api-docs` in your mobile browser

3. **Test Direct Connection**
   Open your mobile browser and navigate to:
   ```
   http://************:3000/health
   ```
   (Replace with your actual IP)

4. **Firewall Settings**
   - Windows: Allow Node.js through Windows Firewall
   - Mac: System Preferences > Security & Privacy > Firewall
   - Temporarily disable firewall to test

5. **Alternative Solutions**
   - Use Expo tunnel: `npx expo start --tunnel`
   - Use ngrok to expose your local server
   - Run backend on 0.0.0.0 instead of localhost

## Network Configuration Details

### Current Configuration
- **Development API**: `http://************:3000/api`
- **Health Check**: `http://************:3000/health`
- **API Docs**: `http://************:3000/api-docs`

### Automatic IP Detection
The app tries to automatically detect your IP address from Expo's development server. If this fails, it falls back to the configured IP in `src/config/api.ts`.

### Testing URLs
You can test these URLs in your mobile browser:
- Health: http://************:3000/health
- API Docs: http://************:3000/api-docs
- Sample API: http://************:3000/api/listings

## Advanced Setup

### Using Expo Tunnel
If local network doesn't work, use Expo's tunnel feature:
```bash
npx expo start --tunnel
```
This creates a public URL that works from anywhere.

### Using ngrok
1. Install ngrok: https://ngrok.com/
2. Expose your backend:
   ```bash
   ngrok http 3000
   ```
3. Update `src/config/api.ts` with the ngrok URL

### Production Setup
For production deployment, update the production configuration in `src/config/api.ts`:
```typescript
production: {
  baseUrl: 'https://your-domain.com/api',
  healthUrl: 'https://your-domain.com/health',
  docsUrl: 'https://your-domain.com/api-docs',
},
```

## Need Help?

1. Check the console logs in the mobile app for detailed error messages
2. Use the "Test API Connection" button in the login screen
3. Verify backend is accessible from your mobile browser
4. Ensure both devices are on the same network
