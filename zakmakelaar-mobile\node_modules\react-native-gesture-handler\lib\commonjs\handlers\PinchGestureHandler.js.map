{"version": 3, "sources": ["PinchGestureHandler.ts"], "names": ["pinchHandlerName", "PinchGestureHandler", "name", "allowedProps", "baseGestureHandlerProps", "config"], "mappings": ";;;;;;;AACA;;AACA;;;;AAWO,MAAMA,gBAAgB,GAAG,qBAAzB;AAEP;AACA;AACA;;;;AAGA;AACA;AACA;AACA;AACO,MAAMC,mBAAmB,GAAG,4BAGjC;AACAC,EAAAA,IAAI,EAAEF,gBADN;AAEAG,EAAAA,YAAY,EAAEC,6CAFd;AAGAC,EAAAA,MAAM,EAAE;AAHR,CAHiC,CAA5B", "sourcesContent": ["import { PinchGestureHandlerEventPayload } from './GestureHandlerEventPayload';\nimport createHandler from './createHandler';\nimport {\n  BaseGestureHandlerProps,\n  baseGestureHandlerProps,\n} from './gestureHandlerCommon';\n\n/**\n * @deprecated PinchGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Pinch()` instead.\n */\nexport interface PinchGestureHandlerProps\n  extends BaseGestureHandlerProps<PinchGestureHandlerEventPayload> {}\n\nexport const pinchHandlerName = 'PinchGestureHandler';\n\n/**\n * @deprecated PinchGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Pinch()` instead.\n */\nexport type PinchGestureHandler = typeof PinchGestureHandler;\n\n/**\n * @deprecated PinchGestureHandler will be removed in the future version of Gesture Handler. Use `Gesture.Pinch()` instead.\n */\n// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; see description on the top of gestureHandlerCommon.ts file\nexport const PinchGestureHandler = createHandler<\n  PinchGestureHandlerProps,\n  PinchGestureHandlerEventPayload\n>({\n  name: pinchHandlerName,\n  allowedProps: baseGestureHandlerProps,\n  config: {},\n});\n"]}