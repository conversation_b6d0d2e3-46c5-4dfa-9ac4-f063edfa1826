{"version": 3, "sources": ["GestureHandlerOrchestrator.ts"], "names": ["GestureHandlerOrchestrator", "constructor", "Set", "scheduleFinishedHandlersCleanup", "handlingChangeSemaphore", "cleanupFinishedHandlers", "<PERSON><PERSON><PERSON><PERSON>", "handler", "reset", "active", "awaiting", "activationIndex", "Number", "MAX_VALUE", "removeHandlerFromOrchestrator", "indexInGestureHandlers", "gestureHandlers", "indexOf", "indexInAwaitingHandlers", "awaitingHandlers", "splice", "awaitingHandlersTags", "delete", "handlerTag", "handlersToRemove", "i", "length", "isFinished", "state", "add", "filter", "has", "hasOtherHandlerToWaitFor", "hasToWaitFor", "<PERSON><PERSON><PERSON><PERSON>", "shouldHandlerWaitForOther", "some", "shouldBeCancelledByFinishedHandler", "shouldBeCancelled", "State", "END", "tryActivate", "cancel", "add<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "handlerState", "CANCELLED", "FAILED", "shouldActivate", "makeActive", "ACTIVE", "fail", "BEGAN", "shouldBeCancelledBy", "shouldHandlerBeCancelledBy", "cleanupAwaitingHandlers", "should<PERSON>ait", "onHandlerStateChange", "newState", "oldState", "sendIfDisabled", "enabled", "sendEvent", "UNDETERMINED", "includes", "currentState", "shouldResetProgress", "push", "recordHandlerIfNotPresent", "MAX_SAFE_INTEGER", "shouldWaitForHandlerFailure", "shouldRequireToWaitForFailure", "canRunSimultaneously", "gh1", "gh2", "shouldRecognizeSimultaneously", "shouldBeCancelledByOther", "handlerPointers", "getTrackedPointersID", "otherPointers", "PointerTracker", "shareCommonPointers", "delegate", "view", "checkOverlap", "isPointerWithinBothBounds", "pointer", "point", "tracker", "getLastAbsoluteCoords", "isPointerInBounds", "cancelMouseAndPenGestures", "<PERSON><PERSON><PERSON><PERSON>", "for<PERSON>ach", "pointerType", "PointerType", "MOUSE", "STYLUS", "resetTracker", "instance", "_instance"], "mappings": ";;;;;;;AAAA;;AACA;;AAGA;;;;;;AAEe,MAAMA,0BAAN,CAAiC;AAU9C;AACA;AACQC,EAAAA,WAAW,GAAG;AAAA,6CATuB,EASvB;;AAAA,8CARwB,EAQxB;;AAAA,kDAPsB,IAAIC,GAAJ,EAOtB;;AAAA,qDALY,CAKZ;;AAAA,6CAJI,CAIJ;AAAE;;AAEhBC,EAAAA,+BAA+B,GAAS;AAC9C,QAAI,KAAKC,uBAAL,KAAiC,CAArC,EAAwC;AACtC,WAAKC,uBAAL;AACD;AACF;;AAEOC,EAAAA,YAAY,CAACC,OAAD,EAAiC;AACnDA,IAAAA,OAAO,CAACC,KAAR;AACAD,IAAAA,OAAO,CAACE,MAAR,GAAiB,KAAjB;AACAF,IAAAA,OAAO,CAACG,QAAR,GAAmB,KAAnB;AACAH,IAAAA,OAAO,CAACI,eAAR,GAA0BC,MAAM,CAACC,SAAjC;AACD;;AAEMC,EAAAA,6BAA6B,CAACP,OAAD,EAAiC;AACnE,UAAMQ,sBAAsB,GAAG,KAAKC,eAAL,CAAqBC,OAArB,CAA6BV,OAA7B,CAA/B;AACA,UAAMW,uBAAuB,GAAG,KAAKC,gBAAL,CAAsBF,OAAtB,CAA8BV,OAA9B,CAAhC;;AAEA,QAAIQ,sBAAsB,IAAI,CAA9B,EAAiC;AAC/B,WAAKC,eAAL,CAAqBI,MAArB,CAA4BL,sBAA5B,EAAoD,CAApD;AACD;;AAED,QAAIG,uBAAuB,IAAI,CAA/B,EAAkC;AAChC,WAAKC,gBAAL,CAAsBC,MAAtB,CAA6BF,uBAA7B,EAAsD,CAAtD;AACA,WAAKG,oBAAL,CAA0BC,MAA1B,CAAiCf,OAAO,CAACgB,UAAzC;AACD;AACF;;AAEOlB,EAAAA,uBAAuB,GAAS;AACtC,UAAMmB,gBAAgB,GAAG,IAAItB,GAAJ,EAAzB;;AAEA,SAAK,IAAIuB,CAAC,GAAG,KAAKT,eAAL,CAAqBU,MAArB,GAA8B,CAA3C,EAA8CD,CAAC,IAAI,CAAnD,EAAsD,EAAEA,CAAxD,EAA2D;AACzD,YAAMlB,OAAO,GAAG,KAAKS,eAAL,CAAqBS,CAArB,CAAhB;;AAEA,UAAI,KAAKE,UAAL,CAAgBpB,OAAO,CAACqB,KAAxB,KAAkC,CAACrB,OAAO,CAACG,QAA/C,EAAyD;AACvD,aAAKJ,YAAL,CAAkBC,OAAlB;AACAiB,QAAAA,gBAAgB,CAACK,GAAjB,CAAqBtB,OAArB;AACD;AACF;;AAED,SAAKS,eAAL,GAAuB,KAAKA,eAAL,CAAqBc,MAArB,CACpBvB,OAAD,IAAa,CAACiB,gBAAgB,CAACO,GAAjB,CAAqBxB,OAArB,CADO,CAAvB;AAGD;;AAEOyB,EAAAA,wBAAwB,CAACzB,OAAD,EAAoC;AAClE,UAAM0B,YAAY,GAAIC,YAAD,IAAmC;AACtD,aACE,CAAC,KAAKP,UAAL,CAAgBO,YAAY,CAACN,KAA7B,CAAD,IACA,KAAKO,yBAAL,CAA+B5B,OAA/B,EAAwC2B,YAAxC,CAFF;AAID,KALD;;AAOA,WAAO,KAAKlB,eAAL,CAAqBoB,IAArB,CAA0BH,YAA1B,CAAP;AACD;;AAEOI,EAAAA,kCAAkC,CACxC9B,OADwC,EAE/B;AACT,UAAM+B,iBAAiB,GAAIJ,YAAD,IAAmC;AAC3D,aACE,KAAKC,yBAAL,CAA+B5B,OAA/B,EAAwC2B,YAAxC,KACAA,YAAY,CAACN,KAAb,KAAuBW,aAAMC,GAF/B;AAID,KALD;;AAOA,WAAO,KAAKxB,eAAL,CAAqBoB,IAArB,CAA0BE,iBAA1B,CAAP;AACD;;AAEOG,EAAAA,WAAW,CAAClC,OAAD,EAAiC;AAClD,QAAI,KAAK8B,kCAAL,CAAwC9B,OAAxC,CAAJ,EAAsD;AACpDA,MAAAA,OAAO,CAACmC,MAAR;AACA;AACD;;AAED,QAAI,KAAKV,wBAAL,CAA8BzB,OAA9B,CAAJ,EAA4C;AAC1C,WAAKoC,kBAAL,CAAwBpC,OAAxB;AACA;AACD;;AAED,UAAMqC,YAAY,GAAGrC,OAAO,CAACqB,KAA7B;;AAEA,QAAIgB,YAAY,KAAKL,aAAMM,SAAvB,IAAoCD,YAAY,KAAKL,aAAMO,MAA/D,EAAuE;AACrE;AACD;;AAED,QAAI,KAAKC,cAAL,CAAoBxC,OAApB,CAAJ,EAAkC;AAChC,WAAKyC,UAAL,CAAgBzC,OAAhB;AACA;AACD;;AAED,QAAIqC,YAAY,KAAKL,aAAMU,MAA3B,EAAmC;AACjC1C,MAAAA,OAAO,CAAC2C,IAAR;AACA;AACD;;AAED,QAAIN,YAAY,KAAKL,aAAMY,KAA3B,EAAkC;AAChC5C,MAAAA,OAAO,CAACmC,MAAR;AACD;AACF;;AAEOK,EAAAA,cAAc,CAACxC,OAAD,EAAoC;AACxD,UAAM6C,mBAAmB,GAAIlB,YAAD,IAAmC;AAC7D,aAAO,KAAKmB,0BAAL,CAAgC9C,OAAhC,EAAyC2B,YAAzC,CAAP;AACD,KAFD;;AAIA,WAAO,CAAC,KAAKlB,eAAL,CAAqBoB,IAArB,CAA0BgB,mBAA1B,CAAR;AACD;;AAEOE,EAAAA,uBAAuB,CAAC/C,OAAD,EAAiC;AAC9D,UAAMgD,UAAU,GAAIrB,YAAD,IAAmC;AACpD,aACE,CAACA,YAAY,CAACxB,QAAd,IACA,KAAKyB,yBAAL,CAA+BD,YAA/B,EAA6C3B,OAA7C,CAFF;AAID,KALD;;AAOA,SAAK,MAAM2B,YAAX,IAA2B,KAAKf,gBAAhC,EAAkD;AAChD,UAAIoC,UAAU,CAACrB,YAAD,CAAd,EAA8B;AAC5B,aAAK5B,YAAL,CAAkB4B,YAAlB;AACA,aAAKb,oBAAL,CAA0BC,MAA1B,CAAiCY,YAAY,CAACX,UAA9C;AACD;AACF;;AAED,SAAKJ,gBAAL,GAAwB,KAAKA,gBAAL,CAAsBW,MAAtB,CAA8BI,YAAD,IACnD,KAAKb,oBAAL,CAA0BU,GAA1B,CAA8BG,YAAY,CAACX,UAA3C,CADsB,CAAxB;AAGD;;AAEMiC,EAAAA,oBAAoB,CACzBjD,OADyB,EAEzBkD,QAFyB,EAGzBC,QAHyB,EAIzBC,cAJyB,EAKnB;AACN,QAAI,CAACpD,OAAO,CAACqD,OAAT,IAAoB,CAACD,cAAzB,EAAyC;AACvC;AACD;;AAED,SAAKvD,uBAAL,IAAgC,CAAhC;;AAEA,QAAI,KAAKuB,UAAL,CAAgB8B,QAAhB,CAAJ,EAA+B;AAC7B,WAAK,MAAMvB,YAAX,IAA2B,KAAKf,gBAAhC,EAAkD;AAChD,YACE,CAAC,KAAKgB,yBAAL,CAA+BD,YAA/B,EAA6C3B,OAA7C,CAAD,IACA,CAAC,KAAKc,oBAAL,CAA0BU,GAA1B,CAA8BG,YAAY,CAACX,UAA3C,CAFH,EAGE;AACA;AACD;;AAED,YAAIkC,QAAQ,KAAKlB,aAAMC,GAAvB,EAA4B;AAC1B,eAAKC,WAAL,CAAiBP,YAAjB;AACA;AACD;;AAEDA,QAAAA,YAAY,CAACQ,MAAb;;AAEA,YAAIR,YAAY,CAACN,KAAb,KAAuBW,aAAMC,GAAjC,EAAsC;AACpC;AACA;AACA;AACA;AACAN,UAAAA,YAAY,CAAC2B,SAAb,CAAuBtB,aAAMM,SAA7B,EAAwCN,aAAMY,KAA9C;AACD;;AAEDjB,QAAAA,YAAY,CAACxB,QAAb,GAAwB,KAAxB;AACD;AACF;;AAED,QAAI+C,QAAQ,KAAKlB,aAAMU,MAAvB,EAA+B;AAC7B,WAAKR,WAAL,CAAiBlC,OAAjB;AACD,KAFD,MAEO,IAAImD,QAAQ,KAAKnB,aAAMU,MAAnB,IAA6BS,QAAQ,KAAKnB,aAAMC,GAApD,EAAyD;AAC9D,UAAIjC,OAAO,CAACE,MAAZ,EAAoB;AAClBF,QAAAA,OAAO,CAACsD,SAAR,CAAkBJ,QAAlB,EAA4BC,QAA5B;AACD,OAFD,MAEO,IACLA,QAAQ,KAAKnB,aAAMU,MAAnB,KACCQ,QAAQ,KAAKlB,aAAMM,SAAnB,IAAgCY,QAAQ,KAAKlB,aAAMO,MADpD,CADK,EAGL;AACAvC,QAAAA,OAAO,CAACsD,SAAR,CAAkBJ,QAAlB,EAA4BlB,aAAMY,KAAlC;AACD;AACF,KATM,MASA,IACLO,QAAQ,KAAKnB,aAAMuB,YAAnB,IACAL,QAAQ,KAAKlB,aAAMM,SAFd,EAGL;AACAtC,MAAAA,OAAO,CAACsD,SAAR,CAAkBJ,QAAlB,EAA4BC,QAA5B;AACD;;AAED,SAAKtD,uBAAL,IAAgC,CAAhC;AAEA,SAAKD,+BAAL;;AAEA,QAAI,CAAC,KAAKgB,gBAAL,CAAsB4C,QAAtB,CAA+BxD,OAA/B,CAAL,EAA8C;AAC5C,WAAK+C,uBAAL,CAA6B/C,OAA7B;AACD;AACF;;AAEOyC,EAAAA,UAAU,CAACzC,OAAD,EAAiC;AACjD,UAAMyD,YAAY,GAAGzD,OAAO,CAACqB,KAA7B;AAEArB,IAAAA,OAAO,CAACE,MAAR,GAAiB,IAAjB;AACAF,IAAAA,OAAO,CAAC0D,mBAAR,GAA8B,IAA9B;AACA1D,IAAAA,OAAO,CAACI,eAAR,GAA0B,KAAKA,eAAL,EAA1B;;AAEA,SAAK,IAAIc,CAAC,GAAG,KAAKT,eAAL,CAAqBU,MAArB,GAA8B,CAA3C,EAA8CD,CAAC,IAAI,CAAnD,EAAsD,EAAEA,CAAxD,EAA2D;AACzD,UAAI,KAAK4B,0BAAL,CAAgC,KAAKrC,eAAL,CAAqBS,CAArB,CAAhC,EAAyDlB,OAAzD,CAAJ,EAAuE;AACrE,aAAKS,eAAL,CAAqBS,CAArB,EAAwBiB,MAAxB;AACD;AACF;;AAED,SAAK,MAAMR,YAAX,IAA2B,KAAKf,gBAAhC,EAAkD;AAChD,UAAI,KAAKkC,0BAAL,CAAgCnB,YAAhC,EAA8C3B,OAA9C,CAAJ,EAA4D;AAC1D2B,QAAAA,YAAY,CAACxB,QAAb,GAAwB,KAAxB;AACD;AACF;;AAEDH,IAAAA,OAAO,CAACsD,SAAR,CAAkBtB,aAAMU,MAAxB,EAAgCV,aAAMY,KAAtC;;AAEA,QAAIa,YAAY,KAAKzB,aAAMU,MAA3B,EAAmC;AACjC1C,MAAAA,OAAO,CAACsD,SAAR,CAAkBtB,aAAMC,GAAxB,EAA6BD,aAAMU,MAAnC;;AACA,UAAIe,YAAY,KAAKzB,aAAMC,GAA3B,EAAgC;AAC9BjC,QAAAA,OAAO,CAACsD,SAAR,CAAkBtB,aAAMuB,YAAxB,EAAsCvB,aAAMC,GAA5C;AACD;AACF;;AAED,QAAI,CAACjC,OAAO,CAACG,QAAb,EAAuB;AACrB;AACD;;AAEDH,IAAAA,OAAO,CAACG,QAAR,GAAmB,KAAnB;AAEA,SAAKS,gBAAL,GAAwB,KAAKA,gBAAL,CAAsBW,MAAtB,CACrBI,YAAD,IAAkBA,YAAY,KAAK3B,OADb,CAAxB;AAGD;;AAEOoC,EAAAA,kBAAkB,CAACpC,OAAD,EAAiC;AACzD,QAAI,KAAKY,gBAAL,CAAsB4C,QAAtB,CAA+BxD,OAA/B,CAAJ,EAA6C;AAC3C;AACD;;AAED,SAAKY,gBAAL,CAAsB+C,IAAtB,CAA2B3D,OAA3B;AACA,SAAKc,oBAAL,CAA0BQ,GAA1B,CAA8BtB,OAAO,CAACgB,UAAtC;AAEAhB,IAAAA,OAAO,CAACG,QAAR,GAAmB,IAAnB;AACAH,IAAAA,OAAO,CAACI,eAAR,GAA0B,KAAKA,eAAL,EAA1B;AACD;;AAEMwD,EAAAA,yBAAyB,CAAC5D,OAAD,EAAiC;AAC/D,QAAI,KAAKS,eAAL,CAAqB+C,QAArB,CAA8BxD,OAA9B,CAAJ,EAA4C;AAC1C;AACD;;AAED,SAAKS,eAAL,CAAqBkD,IAArB,CAA0B3D,OAA1B;AAEAA,IAAAA,OAAO,CAACE,MAAR,GAAiB,KAAjB;AACAF,IAAAA,OAAO,CAACG,QAAR,GAAmB,KAAnB;AACAH,IAAAA,OAAO,CAACI,eAAR,GAA0BC,MAAM,CAACwD,gBAAjC;AACD;;AAEOjC,EAAAA,yBAAyB,CAC/B5B,OAD+B,EAE/B2B,YAF+B,EAGtB;AACT,WACE3B,OAAO,KAAK2B,YAAZ,KACC3B,OAAO,CAAC8D,2BAAR,CAAoCnC,YAApC,KACCA,YAAY,CAACoC,6BAAb,CAA2C/D,OAA3C,CAFF,CADF;AAKD;;AAEOgE,EAAAA,oBAAoB,CAC1BC,GAD0B,EAE1BC,GAF0B,EAGjB;AACT,WACED,GAAG,KAAKC,GAAR,IACAD,GAAG,CAACE,6BAAJ,CAAkCD,GAAlC,CADA,IAEAA,GAAG,CAACC,6BAAJ,CAAkCF,GAAlC,CAHF;AAKD;;AAEOnB,EAAAA,0BAA0B,CAChC9C,OADgC,EAEhC2B,YAFgC,EAGvB;AACT,QAAI,KAAKqC,oBAAL,CAA0BhE,OAA1B,EAAmC2B,YAAnC,CAAJ,EAAsD;AACpD,aAAO,KAAP;AACD;;AAED,QAAI3B,OAAO,CAACG,QAAR,IAAoBH,OAAO,CAACqB,KAAR,KAAkBW,aAAMU,MAAhD,EAAwD;AACtD;AACA,aAAO1C,OAAO,CAACoE,wBAAR,CAAiCzC,YAAjC,CAAP;AACD;;AAED,UAAM0C,eAAyB,GAAGrE,OAAO,CAACsE,oBAAR,EAAlC;AACA,UAAMC,aAAuB,GAAG5C,YAAY,CAAC2C,oBAAb,EAAhC;;AAEA,QACE,CAACE,wBAAeC,mBAAf,CAAmCJ,eAAnC,EAAoDE,aAApD,CAAD,IACAvE,OAAO,CAAC0E,QAAR,CAAiBC,IAAjB,KAA0BhD,YAAY,CAAC+C,QAAb,CAAsBC,IAFlD,EAGE;AACA,aAAO,KAAKC,YAAL,CAAkB5E,OAAlB,EAA2B2B,YAA3B,CAAP;AACD;;AAED,WAAO,IAAP;AACD;;AAEOiD,EAAAA,YAAY,CAClB5E,OADkB,EAElB2B,YAFkB,EAGT;AACT;AACA;AACA;AAEA;AAEA,UAAMkD,yBAAyB,GAAIC,OAAD,IAAqB;AACrD,YAAMC,KAAK,GAAG/E,OAAO,CAACgF,OAAR,CAAgBC,qBAAhB,CAAsCH,OAAtC,CAAd;AAEA,aACE9E,OAAO,CAAC0E,QAAR,CAAiBQ,iBAAjB,CAAmCH,KAAnC,KACApD,YAAY,CAAC+C,QAAb,CAAsBQ,iBAAtB,CAAwCH,KAAxC,CAFF;AAID,KAPD;;AASA,WAAO/E,OAAO,CAACsE,oBAAR,GAA+BzC,IAA/B,CAAoCgD,yBAApC,CAAP;AACD;;AAEOzD,EAAAA,UAAU,CAACC,KAAD,EAAwB;AACxC,WACEA,KAAK,KAAKW,aAAMC,GAAhB,IAAuBZ,KAAK,KAAKW,aAAMO,MAAvC,IAAiDlB,KAAK,KAAKW,aAAMM,SADnE;AAGD,GA1V6C,CA4V9C;AACA;AACA;AACA;AACA;AACA;;;AACO6C,EAAAA,yBAAyB,CAACC,cAAD,EAAwC;AACtE,SAAK3E,eAAL,CAAqB4E,OAArB,CAA8BrF,OAAD,IAA8B;AACzD,UACEA,OAAO,CAACsF,WAAR,KAAwBC,yBAAYC,KAApC,IACAxF,OAAO,CAACsF,WAAR,KAAwBC,yBAAYE,MAFtC,EAGE;AACA;AACD;;AAED,UAAIzF,OAAO,KAAKoF,cAAhB,EAAgC;AAC9BpF,QAAAA,OAAO,CAACmC,MAAR;AACD,OAFD,MAEO;AACL;AACA;AACA;AACA;AACA;AACA;AACA;AACAnC,QAAAA,OAAO,CAACgF,OAAR,CAAgBU,YAAhB;AACD;AACF,KApBD;AAqBD;;AAEyB,aAARC,QAAQ,GAA+B;AACvD,QAAI,CAAClG,0BAA0B,CAACmG,SAAhC,EAA2C;AACzCnG,MAAAA,0BAA0B,CAACmG,SAA3B,GAAuC,IAAInG,0BAAJ,EAAvC;AACD;;AAED,WAAOA,0BAA0B,CAACmG,SAAlC;AACD;;AAhY6C;;;;gBAA3BnG,0B", "sourcesContent": ["import { PointerType } from '../../PointerType';\nimport { State } from '../../State';\n\nimport type IGestureHandler from '../handlers/IGestureHandler';\nimport PointerTracker from './PointerTracker';\n\nexport default class GestureHandlerOrchestrator {\n  private static _instance: GestureHandlerOrchestrator;\n\n  private gestureHandlers: IGestureHandler[] = [];\n  private awaitingHandlers: IGestureHandler[] = [];\n  private awaitingHandlersTags: Set<number> = new Set();\n\n  private handlingChangeSemaphore = 0;\n  private activationIndex = 0;\n\n  // Private beacuse of Singleton\n  // eslint-disable-next-line no-useless-constructor, @typescript-eslint/no-empty-function\n  private constructor() {}\n\n  private scheduleFinishedHandlersCleanup(): void {\n    if (this.handlingChangeSemaphore === 0) {\n      this.cleanupFinishedHandlers();\n    }\n  }\n\n  private cleanHandler(handler: IGestureHandler): void {\n    handler.reset();\n    handler.active = false;\n    handler.awaiting = false;\n    handler.activationIndex = Number.MAX_VALUE;\n  }\n\n  public removeHandlerFromOrchestrator(handler: IGestureHandler): void {\n    const indexInGestureHandlers = this.gestureHandlers.indexOf(handler);\n    const indexInAwaitingHandlers = this.awaitingHandlers.indexOf(handler);\n\n    if (indexInGestureHandlers >= 0) {\n      this.gestureHandlers.splice(indexInGestureHandlers, 1);\n    }\n\n    if (indexInAwaitingHandlers >= 0) {\n      this.awaitingHandlers.splice(indexInAwaitingHandlers, 1);\n      this.awaitingHandlersTags.delete(handler.handlerTag);\n    }\n  }\n\n  private cleanupFinishedHandlers(): void {\n    const handlersToRemove = new Set<IGestureHandler>();\n\n    for (let i = this.gestureHandlers.length - 1; i >= 0; --i) {\n      const handler = this.gestureHandlers[i];\n\n      if (this.isFinished(handler.state) && !handler.awaiting) {\n        this.cleanHandler(handler);\n        handlersToRemove.add(handler);\n      }\n    }\n\n    this.gestureHandlers = this.gestureHandlers.filter(\n      (handler) => !handlersToRemove.has(handler)\n    );\n  }\n\n  private hasOtherHandlerToWaitFor(handler: IGestureHandler): boolean {\n    const hasToWaitFor = (otherHandler: IGestureHandler) => {\n      return (\n        !this.isFinished(otherHandler.state) &&\n        this.shouldHandlerWaitForOther(handler, otherHandler)\n      );\n    };\n\n    return this.gestureHandlers.some(hasToWaitFor);\n  }\n\n  private shouldBeCancelledByFinishedHandler(\n    handler: IGestureHandler\n  ): boolean {\n    const shouldBeCancelled = (otherHandler: IGestureHandler) => {\n      return (\n        this.shouldHandlerWaitForOther(handler, otherHandler) &&\n        otherHandler.state === State.END\n      );\n    };\n\n    return this.gestureHandlers.some(shouldBeCancelled);\n  }\n\n  private tryActivate(handler: IGestureHandler): void {\n    if (this.shouldBeCancelledByFinishedHandler(handler)) {\n      handler.cancel();\n      return;\n    }\n\n    if (this.hasOtherHandlerToWaitFor(handler)) {\n      this.addAwaitingHandler(handler);\n      return;\n    }\n\n    const handlerState = handler.state;\n\n    if (handlerState === State.CANCELLED || handlerState === State.FAILED) {\n      return;\n    }\n\n    if (this.shouldActivate(handler)) {\n      this.makeActive(handler);\n      return;\n    }\n\n    if (handlerState === State.ACTIVE) {\n      handler.fail();\n      return;\n    }\n\n    if (handlerState === State.BEGAN) {\n      handler.cancel();\n    }\n  }\n\n  private shouldActivate(handler: IGestureHandler): boolean {\n    const shouldBeCancelledBy = (otherHandler: IGestureHandler) => {\n      return this.shouldHandlerBeCancelledBy(handler, otherHandler);\n    };\n\n    return !this.gestureHandlers.some(shouldBeCancelledBy);\n  }\n\n  private cleanupAwaitingHandlers(handler: IGestureHandler): void {\n    const shouldWait = (otherHandler: IGestureHandler) => {\n      return (\n        !otherHandler.awaiting &&\n        this.shouldHandlerWaitForOther(otherHandler, handler)\n      );\n    };\n\n    for (const otherHandler of this.awaitingHandlers) {\n      if (shouldWait(otherHandler)) {\n        this.cleanHandler(otherHandler);\n        this.awaitingHandlersTags.delete(otherHandler.handlerTag);\n      }\n    }\n\n    this.awaitingHandlers = this.awaitingHandlers.filter((otherHandler) =>\n      this.awaitingHandlersTags.has(otherHandler.handlerTag)\n    );\n  }\n\n  public onHandlerStateChange(\n    handler: IGestureHandler,\n    newState: State,\n    oldState: State,\n    sendIfDisabled?: boolean\n  ): void {\n    if (!handler.enabled && !sendIfDisabled) {\n      return;\n    }\n\n    this.handlingChangeSemaphore += 1;\n\n    if (this.isFinished(newState)) {\n      for (const otherHandler of this.awaitingHandlers) {\n        if (\n          !this.shouldHandlerWaitForOther(otherHandler, handler) ||\n          !this.awaitingHandlersTags.has(otherHandler.handlerTag)\n        ) {\n          continue;\n        }\n\n        if (newState !== State.END) {\n          this.tryActivate(otherHandler);\n          continue;\n        }\n\n        otherHandler.cancel();\n\n        if (otherHandler.state === State.END) {\n          // Handle edge case, where discrete gestures end immediately after activation thus\n          // their state is set to END and when the gesture they are waiting for activates they\n          // should be cancelled, however `cancel` was never sent as gestures were already in the END state.\n          // Send synthetic BEGAN -> CANCELLED to properly handle JS logic\n          otherHandler.sendEvent(State.CANCELLED, State.BEGAN);\n        }\n\n        otherHandler.awaiting = false;\n      }\n    }\n\n    if (newState === State.ACTIVE) {\n      this.tryActivate(handler);\n    } else if (oldState === State.ACTIVE || oldState === State.END) {\n      if (handler.active) {\n        handler.sendEvent(newState, oldState);\n      } else if (\n        oldState === State.ACTIVE &&\n        (newState === State.CANCELLED || newState === State.FAILED)\n      ) {\n        handler.sendEvent(newState, State.BEGAN);\n      }\n    } else if (\n      oldState !== State.UNDETERMINED ||\n      newState !== State.CANCELLED\n    ) {\n      handler.sendEvent(newState, oldState);\n    }\n\n    this.handlingChangeSemaphore -= 1;\n\n    this.scheduleFinishedHandlersCleanup();\n\n    if (!this.awaitingHandlers.includes(handler)) {\n      this.cleanupAwaitingHandlers(handler);\n    }\n  }\n\n  private makeActive(handler: IGestureHandler): void {\n    const currentState = handler.state;\n\n    handler.active = true;\n    handler.shouldResetProgress = true;\n    handler.activationIndex = this.activationIndex++;\n\n    for (let i = this.gestureHandlers.length - 1; i >= 0; --i) {\n      if (this.shouldHandlerBeCancelledBy(this.gestureHandlers[i], handler)) {\n        this.gestureHandlers[i].cancel();\n      }\n    }\n\n    for (const otherHandler of this.awaitingHandlers) {\n      if (this.shouldHandlerBeCancelledBy(otherHandler, handler)) {\n        otherHandler.awaiting = false;\n      }\n    }\n\n    handler.sendEvent(State.ACTIVE, State.BEGAN);\n\n    if (currentState !== State.ACTIVE) {\n      handler.sendEvent(State.END, State.ACTIVE);\n      if (currentState !== State.END) {\n        handler.sendEvent(State.UNDETERMINED, State.END);\n      }\n    }\n\n    if (!handler.awaiting) {\n      return;\n    }\n\n    handler.awaiting = false;\n\n    this.awaitingHandlers = this.awaitingHandlers.filter(\n      (otherHandler) => otherHandler !== handler\n    );\n  }\n\n  private addAwaitingHandler(handler: IGestureHandler): void {\n    if (this.awaitingHandlers.includes(handler)) {\n      return;\n    }\n\n    this.awaitingHandlers.push(handler);\n    this.awaitingHandlersTags.add(handler.handlerTag);\n\n    handler.awaiting = true;\n    handler.activationIndex = this.activationIndex++;\n  }\n\n  public recordHandlerIfNotPresent(handler: IGestureHandler): void {\n    if (this.gestureHandlers.includes(handler)) {\n      return;\n    }\n\n    this.gestureHandlers.push(handler);\n\n    handler.active = false;\n    handler.awaiting = false;\n    handler.activationIndex = Number.MAX_SAFE_INTEGER;\n  }\n\n  private shouldHandlerWaitForOther(\n    handler: IGestureHandler,\n    otherHandler: IGestureHandler\n  ): boolean {\n    return (\n      handler !== otherHandler &&\n      (handler.shouldWaitForHandlerFailure(otherHandler) ||\n        otherHandler.shouldRequireToWaitForFailure(handler))\n    );\n  }\n\n  private canRunSimultaneously(\n    gh1: IGestureHandler,\n    gh2: IGestureHandler\n  ): boolean {\n    return (\n      gh1 === gh2 ||\n      gh1.shouldRecognizeSimultaneously(gh2) ||\n      gh2.shouldRecognizeSimultaneously(gh1)\n    );\n  }\n\n  private shouldHandlerBeCancelledBy(\n    handler: IGestureHandler,\n    otherHandler: IGestureHandler\n  ): boolean {\n    if (this.canRunSimultaneously(handler, otherHandler)) {\n      return false;\n    }\n\n    if (handler.awaiting || handler.state === State.ACTIVE) {\n      // For now it always returns false\n      return handler.shouldBeCancelledByOther(otherHandler);\n    }\n\n    const handlerPointers: number[] = handler.getTrackedPointersID();\n    const otherPointers: number[] = otherHandler.getTrackedPointersID();\n\n    if (\n      !PointerTracker.shareCommonPointers(handlerPointers, otherPointers) &&\n      handler.delegate.view !== otherHandler.delegate.view\n    ) {\n      return this.checkOverlap(handler, otherHandler);\n    }\n\n    return true;\n  }\n\n  private checkOverlap(\n    handler: IGestureHandler,\n    otherHandler: IGestureHandler\n  ): boolean {\n    // If handlers don't have common pointers, default return value is false.\n    // However, if at least on pointer overlaps with both handlers, we return true\n    // This solves issue in overlapping parents example\n\n    // TODO: Find better way to handle that issue, for example by activation order and handler cancelling\n\n    const isPointerWithinBothBounds = (pointer: number) => {\n      const point = handler.tracker.getLastAbsoluteCoords(pointer);\n\n      return (\n        handler.delegate.isPointerInBounds(point) &&\n        otherHandler.delegate.isPointerInBounds(point)\n      );\n    };\n\n    return handler.getTrackedPointersID().some(isPointerWithinBothBounds);\n  }\n\n  private isFinished(state: State): boolean {\n    return (\n      state === State.END || state === State.FAILED || state === State.CANCELLED\n    );\n  }\n\n  // This function is called when handler receives touchdown event\n  // If handler is using mouse or pen as a pointer and any handler receives touch event,\n  // mouse/pen event dissappears - it doesn't send onPointerCancel nor onPointerUp (and others)\n  // This became a problem because handler was left at active state without any signal to end or fail\n  // To handle this, when new touch event is received, we loop through active handlers and check which type of\n  // pointer they're using. If there are any handler with mouse/pen as a pointer, we cancel them\n  public cancelMouseAndPenGestures(currentHandler: IGestureHandler): void {\n    this.gestureHandlers.forEach((handler: IGestureHandler) => {\n      if (\n        handler.pointerType !== PointerType.MOUSE &&\n        handler.pointerType !== PointerType.STYLUS\n      ) {\n        return;\n      }\n\n      if (handler !== currentHandler) {\n        handler.cancel();\n      } else {\n        // Handler that received touch event should have its pointer tracker reset\n        // This allows handler to smoothly change from mouse/pen to touch\n        // The drawback is, that when we try to use mouse/pen one more time, it doesn't send onPointerDown at the first time\n        // so it is required to click two times to get handler to work\n        //\n        // However, handler will receive manually created onPointerEnter that is triggered in EventManager in onPointerMove method.\n        // There may be possibility to use that fact to make handler respond properly to first mouse click\n        handler.tracker.resetTracker();\n      }\n    });\n  }\n\n  public static get instance(): GestureHandlerOrchestrator {\n    if (!GestureHandlerOrchestrator._instance) {\n      GestureHandlerOrchestrator._instance = new GestureHandlerOrchestrator();\n    }\n\n    return GestureHandlerOrchestrator._instance;\n  }\n}\n"]}