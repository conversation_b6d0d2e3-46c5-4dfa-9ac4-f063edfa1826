{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'development',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-07-05 22:40:27'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '6ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-05 22:41:51'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/swagger-ui.css',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-05 22:41:51'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/swagger-ui-init.js',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '0ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-05 22:41:51'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/swagger-ui-bundle.js',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-05 22:41:51'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/swagger-ui-standalone-preset.js',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-05 22:41:51'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '7ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-07-05 22:42:07'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?sortBy=dateAdded&sortOrder=desc&page=1&limit=20',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 200,
  responseTime: '17ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-05 22:42:07'
}
{
  component: 'database',
  service: 'zakmakelaar-api',
  operation: 'search',
  collection: 'listings',
  duration: '6ms',
  level: 'info',
  message: 'Database Operation',
  timestamp: '2025-07-05 22:43:47'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api/listings?sortBy=price&sortOrder=desc&page=1&limit=20',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 200,
  responseTime: '13ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-05 22:43:47'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-05 22:45:00'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-05 22:46:31'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/swagger-ui.css',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-05 22:46:31'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/swagger-ui-bundle.js',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '0ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-05 22:46:31'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/swagger-ui-standalone-preset.js',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-05 22:46:31'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/swagger-ui-init.js',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-05 22:46:31'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/favicon-32x32.png',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-05 22:46:31'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '114558ms',
  listingsProcessed: 41,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-05 22:46:54'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-05 22:50:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '110714ms',
  listingsProcessed: 41,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-05 22:51:50'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-05 22:55:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '110688ms',
  listingsProcessed: 41,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-05 22:56:50'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-05 23:00:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '111752ms',
  listingsProcessed: 41,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-05 23:01:51'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-05 23:05:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '111075ms',
  listingsProcessed: 41,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-05 23:06:51'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-05 23:10:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '114975ms',
  listingsProcessed: 41,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-05 23:11:54'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-05 23:15:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '112151ms',
  listingsProcessed: 41,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-05 23:16:52'
}
{
  service: 'zakmakelaar-api',
  port: '3000',
  environment: 'development',
  endpoints: {
    api: 'http://localhost:3000',
    docs: 'http://localhost:3000/api-docs',
    health: 'http://localhost:3000/health'
  },
  level: 'info',
  message: '🚀 ZakMakelaar API Server Started',
  timestamp: '2025-07-05 23:29:02'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-05 23:30:00'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '117768ms',
  listingsProcessed: 41,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-05 23:31:57'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-05 23:35:00'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '6ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-05 23:35:40'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/swagger-ui.css',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-05 23:35:40'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/swagger-ui-init.js',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '1ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-05 23:35:40'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/swagger-ui-standalone-preset.js',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '3ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-05 23:35:40'
}
{
  component: 'api',
  service: 'zakmakelaar-api',
  method: 'GET',
  url: '/api-docs/swagger-ui-bundle.js',
  ip: '::1',
  userAgent: 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/********* Safari/537.36',
  statusCode: 304,
  responseTime: '2ms',
  userId: null,
  level: 'info',
  message: 'API Request',
  timestamp: '2025-07-05 23:35:40'
}
{
  component: 'scraper',
  service: 'zakmakelaar-api',
  duration: '116683ms',
  listingsProcessed: 41,
  level: 'info',
  message: 'Scheduled scraping completed',
  timestamp: '2025-07-05 23:36:56'
}
{
  component: 'scraper',
  message: 'Starting scheduled scraping (every 5 minutes)',
  level: 'info',
  service: 'zakmakelaar-api',
  timestamp: '2025-07-05 23:40:00'
}
