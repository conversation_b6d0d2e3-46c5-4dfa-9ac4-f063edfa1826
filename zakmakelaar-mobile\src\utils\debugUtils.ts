// Debug utilities for development and troubleshooting

export const debugLog = (message: string, data?: any) => {
  if (__DEV__) {
    console.log(`[DEBUG] ${message}`, data || '');
  }
};

export const debugError = (message: string, error?: any) => {
  if (__DEV__) {
    console.error(`[ERROR] ${message}`, error || '');
  }
};

export const debugWarn = (message: string, data?: any) => {
  if (__DEV__) {
    console.warn(`[WARN] ${message}`, data || '');
  }
};

// Test all font weights to ensure they work
export const testFontWeights = () => {
  if (!__DEV__) return;
  
  const weights = ['100', '200', '300', '400', '500', '600', '700', '800', '900'];
  
  console.log('Testing font weights:');
  weights.forEach(weight => {
    try {
      // This would normally be used in a StyleSheet
      const testStyle = { fontWeight: weight as any };
      console.log(`✓ FontWeight ${weight} - OK`);
    } catch (error) {
      console.error(`✗ FontWeight ${weight} - ERROR:`, error);
    }
  });
};

// Test API connectivity
export const testConnectivity = async () => {
  if (!__DEV__) return;
  
  try {
    const response = await fetch('https://httpbin.org/get', {
      method: 'GET',
      timeout: 5000,
    });
    
    if (response.ok) {
      debugLog('Internet connectivity: OK');
      return true;
    } else {
      debugWarn('Internet connectivity: Limited');
      return false;
    }
  } catch (error) {
    debugError('Internet connectivity: Failed', error);
    return false;
  }
};

// Log device and app information
export const logDeviceInfo = () => {
  if (!__DEV__) return;
  
  try {
    const Constants = require('expo-constants').default;
    
    console.log('=== DEVICE INFO ===');
    console.log('Platform:', Constants.platform);
    console.log('Expo Version:', Constants.expoVersion);
    console.log('App Version:', Constants.manifest?.version);
    console.log('Debug Host:', Constants.manifest?.debuggerHost);
    console.log('===================');
  } catch (error) {
    debugError('Failed to get device info', error);
  }
};

// Performance monitoring
export const performanceMonitor = {
  start: (label: string) => {
    if (__DEV__) {
      console.time(label);
    }
  },
  
  end: (label: string) => {
    if (__DEV__) {
      console.timeEnd(label);
    }
  },
  
  mark: (message: string) => {
    if (__DEV__) {
      console.log(`[PERF] ${message} - ${Date.now()}`);
    }
  },
};

// Error boundary helper
export const logError = (error: Error, errorInfo?: any) => {
  if (__DEV__) {
    console.error('=== ERROR BOUNDARY ===');
    console.error('Error:', error);
    console.error('Error Info:', errorInfo);
    console.error('Stack:', error.stack);
    console.error('=====================');
  }
  
  // In production, you might want to send this to a crash reporting service
  // like Sentry, Bugsnag, etc.
};

// Memory usage (if available)
export const logMemoryUsage = () => {
  if (__DEV__ && (performance as any).memory) {
    const memory = (performance as any).memory;
    console.log('=== MEMORY USAGE ===');
    console.log('Used:', Math.round(memory.usedJSHeapSize / 1024 / 1024) + ' MB');
    console.log('Total:', Math.round(memory.totalJSHeapSize / 1024 / 1024) + ' MB');
    console.log('Limit:', Math.round(memory.jsHeapSizeLimit / 1024 / 1024) + ' MB');
    console.log('===================');
  }
};

// Initialize debug utilities
export const initializeDebug = () => {
  if (__DEV__) {
    debugLog('Debug utilities initialized');
    logDeviceInfo();
    testFontWeights();
    
    // Test connectivity after a short delay
    setTimeout(() => {
      testConnectivity();
    }, 2000);
  }
};
