import React, { useEffect, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  ScrollView,
  TouchableOpacity,
  RefreshControl,
} from "react-native";
import { SafeAreaView } from "react-native-safe-area-context";
import { Ionicons } from "@expo/vector-icons";
import { useAuthStore } from "@/stores/authStore";
import { useListingsStore } from "@/stores/listingsStore";

export default function DashboardScreen() {
  const { user } = useAuthStore();
  const { listings, fetchListings, isLoading } = useListingsStore();
  const [refreshing, setRefreshing] = useState(false);

  // Mock agent status data (will be replaced with real data later)
  const [agentStatus] = useState({
    isActive: true,
    currentTask: "Scanning new listings",
    totalApplications: 12,
    successRate: 25,
    newListingsToday: 8,
    autonomyLevel: 85,
  });

  useEffect(() => {
    fetchListings({ limit: 5 });
  }, []);

  const onRefresh = async () => {
    setRefreshing(true);
    await fetchListings({ limit: 5, refresh: true });
    setRefreshing(false);
  };

  const getStatusColor = (isActive: boolean) => {
    return isActive ? "#34C759" : "#FF3B30";
  };

  const getStatusText = (isActive: boolean) => {
    return isActive ? "Active" : "Inactive";
  };

  return (
    <SafeAreaView style={styles.container}>
      <ScrollView
        style={styles.scrollView}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={onRefresh}
            tintColor="#007AFF"
          />
        }
      >
        {/* Header */}
        <View style={styles.header}>
          <Text style={styles.greeting}>
            Welcome back,{" "}
            {user?.profile?.name || user?.email?.split("@")[0] || "User"}!
          </Text>
          <Text style={styles.subtitle}>Your AI agent is working for you</Text>
        </View>

        {/* Agent Status Card */}
        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>Agent Status</Text>
            <View style={styles.statusIndicator}>
              <View
                style={[
                  styles.statusDot,
                  { backgroundColor: getStatusColor(agentStatus.isActive) },
                ]}
              />
              <Text
                style={[
                  styles.statusText,
                  { color: getStatusColor(agentStatus.isActive) },
                ]}
              >
                {getStatusText(agentStatus.isActive)}
              </Text>
            </View>
          </View>

          <Text style={styles.currentTask}>
            Current Task: {agentStatus.currentTask}
          </Text>

          <View style={styles.autonomyContainer}>
            <Text style={styles.autonomyLabel}>Autonomy Level</Text>
            <View style={styles.autonomyBar}>
              <View
                style={[
                  styles.autonomyFill,
                  { width: `${agentStatus.autonomyLevel}%` },
                ]}
              />
            </View>
            <Text style={styles.autonomyText}>
              {agentStatus.autonomyLevel}%
            </Text>
          </View>
        </View>

        {/* Quick Stats */}
        <View style={styles.statsContainer}>
          <View style={styles.statCard}>
            <Ionicons name="document-text" size={24} color="#007AFF" />
            <Text style={styles.statNumber}>
              {agentStatus.totalApplications}
            </Text>
            <Text style={styles.statLabel}>Applications</Text>
          </View>

          <View style={styles.statCard}>
            <Ionicons name="checkmark-circle" size={24} color="#34C759" />
            <Text style={styles.statNumber}>{agentStatus.successRate}%</Text>
            <Text style={styles.statLabel}>Success Rate</Text>
          </View>

          <View style={styles.statCard}>
            <Ionicons name="home" size={24} color="#FF9500" />
            <Text style={styles.statNumber}>
              {agentStatus.newListingsToday}
            </Text>
            <Text style={styles.statLabel}>New Today</Text>
          </View>
        </View>

        {/* Recent Listings */}
        <View style={styles.card}>
          <View style={styles.cardHeader}>
            <Text style={styles.cardTitle}>Recent Listings</Text>
            <TouchableOpacity>
              <Text style={styles.seeAllText}>See All</Text>
            </TouchableOpacity>
          </View>

          {listings.slice(0, 3).map((listing, index) => (
            <TouchableOpacity
              key={listing.id || index}
              style={styles.listingItem}
            >
              <View style={styles.listingInfo}>
                <Text style={styles.listingTitle} numberOfLines={1}>
                  {listing.title}
                </Text>
                <Text style={styles.listingLocation}>{listing.location}</Text>
                <Text style={styles.listingPrice}>{listing.price}</Text>
              </View>
              <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
            </TouchableOpacity>
          ))}

          {listings.length === 0 && !isLoading && (
            <View style={styles.emptyState}>
              <Ionicons name="home-outline" size={48} color="#8E8E93" />
              <Text style={styles.emptyText}>No listings found</Text>
              <Text style={styles.emptySubtext}>
                Your agent will notify you when new properties match your
                criteria
              </Text>
            </View>
          )}
        </View>

        {/* Quick Actions */}
        <View style={styles.card}>
          <Text style={styles.cardTitle}>Quick Actions</Text>

          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="settings" size={20} color="#007AFF" />
            <Text style={styles.actionText}>Configure Agent</Text>
            <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="search" size={20} color="#007AFF" />
            <Text style={styles.actionText}>Browse Listings</Text>
            <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
          </TouchableOpacity>

          <TouchableOpacity style={styles.actionButton}>
            <Ionicons name="document-text" size={20} color="#007AFF" />
            <Text style={styles.actionText}>View Applications</Text>
            <Ionicons name="chevron-forward" size={20} color="#8E8E93" />
          </TouchableOpacity>
        </View>
      </ScrollView>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1a1a1a",
  },
  scrollView: {
    flex: 1,
  },
  header: {
    padding: 24,
    paddingBottom: 16,
  },
  greeting: {
    fontSize: 24,
    fontWeight: "700",
    color: "#ffffff",
    marginBottom: 4,
  },
  subtitle: {
    fontSize: 16,
    color: "#8E8E93",
  },
  card: {
    backgroundColor: "#2a2a2a",
    marginHorizontal: 24,
    marginBottom: 16,
    borderRadius: 16,
    padding: 20,
  },
  cardHeader: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 16,
  },
  cardTitle: {
    fontSize: 18,
    fontWeight: "600",
    color: "#ffffff",
  },
  statusIndicator: {
    flexDirection: "row",
    alignItems: "center",
  },
  statusDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    marginRight: 6,
  },
  statusText: {
    fontSize: 14,
    fontWeight: "500",
  },
  currentTask: {
    fontSize: 14,
    color: "#8E8E93",
    marginBottom: 16,
  },
  autonomyContainer: {
    flexDirection: "row",
    alignItems: "center",
  },
  autonomyLabel: {
    fontSize: 14,
    color: "#8E8E93",
    marginRight: 12,
    minWidth: 80,
  },
  autonomyBar: {
    flex: 1,
    height: 6,
    backgroundColor: "#3a3a3a",
    borderRadius: 3,
    marginRight: 12,
  },
  autonomyFill: {
    height: "100%",
    backgroundColor: "#007AFF",
    borderRadius: 3,
  },
  autonomyText: {
    fontSize: 14,
    color: "#ffffff",
    fontWeight: "500",
    minWidth: 35,
  },
  statsContainer: {
    flexDirection: "row",
    marginHorizontal: 24,
    marginBottom: 16,
    gap: 12,
  },
  statCard: {
    flex: 1,
    backgroundColor: "#2a2a2a",
    borderRadius: 12,
    padding: 16,
    alignItems: "center",
  },
  statNumber: {
    fontSize: 20,
    fontWeight: "700",
    color: "#ffffff",
    marginTop: 8,
    marginBottom: 4,
  },
  statLabel: {
    fontSize: 12,
    color: "#8E8E93",
    textAlign: "center",
  },
  seeAllText: {
    fontSize: 14,
    color: "#007AFF",
    fontWeight: "500",
  },
  listingItem: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#3a3a3a",
  },
  listingInfo: {
    flex: 1,
  },
  listingTitle: {
    fontSize: 16,
    fontWeight: "500",
    color: "#ffffff",
    marginBottom: 2,
  },
  listingLocation: {
    fontSize: 14,
    color: "#8E8E93",
    marginBottom: 2,
  },
  listingPrice: {
    fontSize: 14,
    color: "#007AFF",
    fontWeight: "500",
  },
  emptyState: {
    alignItems: "center",
    paddingVertical: 32,
  },
  emptyText: {
    fontSize: 16,
    color: "#8E8E93",
    marginTop: 12,
    marginBottom: 4,
  },
  emptySubtext: {
    fontSize: 14,
    color: "#8E8E93",
    textAlign: "center",
    lineHeight: 20,
  },
  actionButton: {
    flexDirection: "row",
    alignItems: "center",
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: "#3a3a3a",
  },
  actionText: {
    flex: 1,
    fontSize: 16,
    color: "#ffffff",
    marginLeft: 12,
  },
});
