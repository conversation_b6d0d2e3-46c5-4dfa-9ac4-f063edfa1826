import React from "react";
import { NavigationContainer, DefaultTheme } from "@react-navigation/native";
import { createStackNavigator } from "@react-navigation/stack";
import { useAuthStore } from "@/stores/authStore";

// Import screens
import LoginScreen from "@/screens/auth/LoginScreen";
import DashboardScreen from "@/screens/dashboard/DashboardScreen";
import LoadingScreen from "@/screens/LoadingScreen";

const Stack = createStackNavigator();

// Create a custom theme without font configurations
const CustomTheme = {
  ...DefaultTheme,
  dark: true,
  colors: {
    ...DefaultTheme.colors,
    primary: "#007AFF",
    background: "#1a1a1a",
    card: "#2a2a2a",
    text: "#ffffff",
    border: "#3a3a3a",
    notification: "#FF3B30",
  },
};

// Ultra-minimal navigation setup to avoid font issues
export default function SimpleNavigator() {
  const { isAuthenticated, isLoading } = useAuthStore();

  if (isLoading) {
    return <LoadingScreen />;
  }

  return (
    <NavigationContainer theme={CustomTheme}>
      <Stack.Navigator
        screenOptions={{
          // Minimal styling - no custom fonts
          headerStyle: {
            backgroundColor: "#1a1a1a",
          },
          headerTintColor: "#ffffff",
          cardStyle: { backgroundColor: "#1a1a1a" },
        }}
      >
        {isAuthenticated ? (
          <Stack.Screen
            name="Dashboard"
            component={DashboardScreen}
            options={{ title: "Dashboard" }}
          />
        ) : (
          <Stack.Screen
            name="Login"
            component={LoginScreen}
            options={{ headerShown: false }}
          />
        )}
      </Stack.Navigator>
    </NavigationContainer>
  );
}
