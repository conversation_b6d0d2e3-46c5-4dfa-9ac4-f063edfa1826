{"version": 3, "sources": ["index.ts"], "names": ["initialize", "Directions", "State", "PointerType", "default", "gestureHandlerRootHOC", "GestureHandlerRootView", "MouseB<PERSON>on", "TapGestureHandler", "ForceTouchGestureHandler", "LongPressGestureHandler", "PanGestureHandler", "PinchGestureHandler", "RotationGestureHandler", "FlingGestureHandler", "createNativeWrapper", "GestureDetector", "GestureObjects", "Gesture", "NativeViewGestureHandler", "RawButton", "BaseButton", "RectButton", "BorderlessButton", "PureNativeButton", "TouchableHighlight", "TouchableNativeFeedback", "TouchableOpacity", "TouchableWithoutFeedback", "ScrollView", "Switch", "TextInput", "DrawerLayoutAndroid", "FlatList", "RefreshControl", "Text", "HoverEffect", "Swipeable", "Pressable", "DrawerLayout", "enableExperimentalWebImplementation", "enableLegacyWebImplementation"], "mappings": "AAAA,SAASA,UAAT,QAA2B,QAA3B;AAEA,SAASC,UAAT,QAA2B,cAA3B;AACA,SAASC,KAAT,QAAsB,SAAtB;AACA,SAASC,WAAT,QAA4B,eAA5B;AACA,SAASC,OAAO,IAAIC,qBAApB,QAAiD,oCAAjD;AACA,SAASD,OAAO,IAAIE,sBAApB,QAAkD,qCAAlD;AAeA,SAASC,WAAT,QAA4B,iCAA5B;AAsBA,SAASC,iBAAT,QAAkC,8BAAlC;AACA,SAASC,wBAAT,QAAyC,qCAAzC;AACA,SAASC,uBAAT,QAAwC,oCAAxC;AACA,SAASC,iBAAT,QAAkC,8BAAlC;AACA,SAASC,mBAAT,QAAoC,gCAApC;AACA,SAASC,sBAAT,QAAuC,mCAAvC;AACA,SAASC,mBAAT,QAAoC,gCAApC;AACA,SAASV,OAAO,IAAIW,mBAApB,QAA+C,gCAA/C;AAEA,SAASC,eAAT,QAAgC,qCAAhC;AACA,SAASC,cAAc,IAAIC,OAA3B,QAA0C,oCAA1C;AAkBA,SAASC,wBAAT,QAAyC,qCAAzC;AAOA,SACEC,SADF,EAEEC,UAFF,EAGEC,UAHF,EAIEC,gBAJF,EAKEC,gBALF,QAMO,6BANP;AAYA,SACEC,kBADF,EAEEC,uBAFF,EAGEC,gBAHF,EAIEC,wBAJF,QAKO,yBALP;AAMA,SACEC,UADF,EAEEC,MAFF,EAGEC,SAHF,EAIEC,mBAJF,EAKEC,QALF,EAMEC,cANF,QAOO,gCAPP;AAQA,SAASC,IAAT,QAAqB,mBAArB;AACA,SAASC,WAAT,QAA4B,kCAA5B;AAyCA,SAAShC,OAAO,IAAIiC,SAApB,QAAqC,wBAArC;AAKA,SAASjC,OAAO,IAAIkC,SAApB,QAAqC,wBAArC;AAUA,SAASlC,OAAO,IAAImC,YAApB,QAAwC,2BAAxC;AAEA,SACEC,mCADF,EAEEC,6BAFF,QAGO,8BAHP;AAKAzC,UAAU", "sourcesContent": ["import { initialize } from './init';\n\nexport { Directions } from './Directions';\nexport { State } from './State';\nexport { PointerType } from './PointerType';\nexport { default as gestureHandlerRootHOC } from './components/gestureHandlerRootHOC';\nexport { default as GestureHandlerRootView } from './components/GestureHandlerRootView';\nexport type {\n  // Event types\n  GestureEvent,\n  HandlerStateChangeEvent,\n  // Event payloads types\n  GestureEventPayload,\n  HandlerStateChangeEventPayload,\n  // Pointer events\n  GestureTouchEvent,\n  TouchData,\n  // New api event types\n  GestureUpdateEvent,\n  GestureStateChangeEvent,\n} from './handlers/gestureHandlerCommon';\nexport { MouseButton } from './handlers/gestureHandlerCommon';\nexport type { GestureType } from './handlers/gestures/gesture';\nexport type {\n  TapGestureHandlerEventPayload,\n  ForceTouchGestureHandlerEventPayload,\n  LongPressGestureHandlerEventPayload,\n  PanGestureHandlerEventPayload,\n  PinchGestureHandlerEventPayload,\n  RotationGestureHandlerEventPayload,\n  NativeViewGestureHandlerPayload,\n  FlingGestureHandlerEventPayload,\n} from './handlers/GestureHandlerEventPayload';\nexport type { TapGestureHandlerProps } from './handlers/TapGestureHandler';\nexport type { ForceTouchGestureHandlerProps } from './handlers/ForceTouchGestureHandler';\nexport type { ForceTouchGestureChangeEventPayload } from './handlers/gestures/forceTouchGesture';\nexport type { LongPressGestureHandlerProps } from './handlers/LongPressGestureHandler';\nexport type { PanGestureHandlerProps } from './handlers/PanGestureHandler';\nexport type { PanGestureChangeEventPayload } from './handlers/gestures/panGesture';\nexport type { PinchGestureHandlerProps } from './handlers/PinchGestureHandler';\nexport type { PinchGestureChangeEventPayload } from './handlers/gestures/pinchGesture';\nexport type { RotationGestureHandlerProps } from './handlers/RotationGestureHandler';\nexport type { FlingGestureHandlerProps } from './handlers/FlingGestureHandler';\nexport { TapGestureHandler } from './handlers/TapGestureHandler';\nexport { ForceTouchGestureHandler } from './handlers/ForceTouchGestureHandler';\nexport { LongPressGestureHandler } from './handlers/LongPressGestureHandler';\nexport { PanGestureHandler } from './handlers/PanGestureHandler';\nexport { PinchGestureHandler } from './handlers/PinchGestureHandler';\nexport { RotationGestureHandler } from './handlers/RotationGestureHandler';\nexport { FlingGestureHandler } from './handlers/FlingGestureHandler';\nexport { default as createNativeWrapper } from './handlers/createNativeWrapper';\nexport type { NativeViewGestureHandlerProps } from './handlers/NativeViewGestureHandler';\nexport { GestureDetector } from './handlers/gestures/GestureDetector';\nexport { GestureObjects as Gesture } from './handlers/gestures/gestureObjects';\nexport type { TapGestureType as TapGesture } from './handlers/gestures/tapGesture';\nexport type { PanGestureType as PanGesture } from './handlers/gestures/panGesture';\nexport type { FlingGestureType as FlingGesture } from './handlers/gestures/flingGesture';\nexport type { LongPressGestureType as LongPressGesture } from './handlers/gestures/longPressGesture';\nexport type { PinchGestureType as PinchGesture } from './handlers/gestures/pinchGesture';\nexport type { RotationGestureType as RotationGesture } from './handlers/gestures/rotationGesture';\nexport type { ForceTouchGestureType as ForceTouchGesture } from './handlers/gestures/forceTouchGesture';\nexport type { NativeGestureType as NativeGesture } from './handlers/gestures/nativeGesture';\nexport type { ManualGestureType as ManualGesture } from './handlers/gestures/manualGesture';\nexport type { HoverGestureType as HoverGesture } from './handlers/gestures/hoverGesture';\nexport type {\n  ComposedGestureType as ComposedGesture,\n  RaceGestureType as RaceGesture,\n  SimultaneousGestureType as SimultaneousGesture,\n  ExclusiveGestureType as ExclusiveGesture,\n} from './handlers/gestures/gestureComposition';\nexport type { GestureStateManagerType as GestureStateManager } from './handlers/gestures/gestureStateManager';\nexport { NativeViewGestureHandler } from './handlers/NativeViewGestureHandler';\nexport type {\n  RawButtonProps,\n  BaseButtonProps,\n  RectButtonProps,\n  BorderlessButtonProps,\n} from './components/GestureButtonsProps';\nexport {\n  RawButton,\n  BaseButton,\n  RectButton,\n  BorderlessButton,\n  PureNativeButton,\n} from './components/GestureButtons';\nexport type {\n  TouchableHighlightProps,\n  TouchableOpacityProps,\n  TouchableWithoutFeedbackProps,\n} from './components/touchables';\nexport {\n  TouchableHighlight,\n  TouchableNativeFeedback,\n  TouchableOpacity,\n  TouchableWithoutFeedback,\n} from './components/touchables';\nexport {\n  ScrollView,\n  Switch,\n  TextInput,\n  DrawerLayoutAndroid,\n  FlatList,\n  RefreshControl,\n} from './components/GestureComponents';\nexport { Text } from './components/Text';\nexport { HoverEffect } from './handlers/gestures/hoverGesture';\nexport type {\n  // Events\n  GestureHandlerGestureEvent,\n  GestureHandlerStateChangeEvent,\n  // Event payloads\n  GestureHandlerGestureEventNativeEvent,\n  GestureHandlerStateChangeNativeEvent,\n  NativeViewGestureHandlerGestureEvent,\n  NativeViewGestureHandlerStateChangeEvent,\n  TapGestureHandlerGestureEvent,\n  TapGestureHandlerStateChangeEvent,\n  ForceTouchGestureHandlerGestureEvent,\n  ForceTouchGestureHandlerStateChangeEvent,\n  LongPressGestureHandlerGestureEvent,\n  LongPressGestureHandlerStateChangeEvent,\n  PanGestureHandlerGestureEvent,\n  PanGestureHandlerStateChangeEvent,\n  PinchGestureHandlerGestureEvent,\n  PinchGestureHandlerStateChangeEvent,\n  RotationGestureHandlerGestureEvent,\n  RotationGestureHandlerStateChangeEvent,\n  FlingGestureHandlerGestureEvent,\n  FlingGestureHandlerStateChangeEvent,\n  // Handlers props\n  NativeViewGestureHandlerProperties,\n  TapGestureHandlerProperties,\n  LongPressGestureHandlerProperties,\n  PanGestureHandlerProperties,\n  PinchGestureHandlerProperties,\n  RotationGestureHandlerProperties,\n  FlingGestureHandlerProperties,\n  ForceTouchGestureHandlerProperties,\n  // Buttons props\n  RawButtonProperties,\n  BaseButtonProperties,\n  RectButtonProperties,\n  BorderlessButtonProperties,\n} from './handlers/gestureHandlerTypesCompat';\n\nexport type { SwipeableProps } from './components/Swipeable';\nexport { default as Swipeable } from './components/Swipeable';\nexport type {\n  PressableProps,\n  PressableStateCallbackType,\n} from './components/Pressable';\nexport { default as Pressable } from './components/Pressable';\n\nexport type {\n  DrawerLayoutProps,\n  DrawerPosition,\n  DrawerState,\n  DrawerType,\n  DrawerLockMode,\n  DrawerKeyboardDismissMode,\n} from './components/DrawerLayout';\nexport { default as DrawerLayout } from './components/DrawerLayout';\n\nexport {\n  enableExperimentalWebImplementation,\n  enableLegacyWebImplementation,\n} from './EnableNewWebImplementation';\n\ninitialize();\n"]}