{"version": 3, "sources": ["PointerEventManager.ts"], "names": ["EventManager", "MouseB<PERSON>on", "EventTypes", "PointerTypeMapping", "calculateViewScale", "tryExtractStylusData", "isPointerInBounds", "PointerType", "POINTER_CAPTURE_EXCLUDE_LIST", "Set", "PointerEventManager", "constructor", "view", "Map", "event", "x", "clientX", "y", "clientY", "adaptedEvent", "mapEvent", "DOWN", "target", "has", "tagName", "setPointerCapture", "pointerId", "markAsInBounds", "trackedPointers", "add", "activePointersCounter", "eventType", "ADDITIONAL_POINTER_DOWN", "onPointerAdd", "onPointerDown", "UP", "releasePointerCapture", "markAsOutOfBounds", "delete", "ADDITIONAL_POINTER_UP", "onPointerRemove", "onPointerUp", "MOVE", "hasPointerCapture", "inBounds", "pointerIndex", "pointersInBounds", "indexOf", "ENTER", "onPointerEnter", "onPointerMove", "LEAVE", "onPointerLeave", "onPointerOutOfBounds", "lastPosition", "CANCEL", "onPointerCancel", "clear", "onPointerMoveOver", "onPointerMoveOut", "mouseButtonsMapper", "set", "LEFT", "MIDDLE", "RIGHT", "BUTTON_4", "BUTTON_5", "Infinity", "registerListeners", "addEventListener", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "pointerCancelCallback", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON>", "pointer<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "lostPointerCaptureCallback", "unregisterListeners", "removeEventListener", "rect", "getBoundingClientRect", "scaleX", "scaleY", "offsetX", "left", "offsetY", "top", "pointerType", "get", "OTHER", "button", "time", "timeStamp", "stylusData", "resetManager"], "mappings": ";;AAAA,OAAOA,YAAP,MAAyB,gBAAzB;AACA,SAASC,WAAT,QAA4B,qCAA5B;AACA,SAAuBC,UAAvB,QAAgD,eAAhD;AACA,SACEC,kBADF,EAEEC,kBAFF,EAGEC,oBAHF,EAIEC,iBAJF,QAKO,UALP;AAMA,SAASC,WAAT,QAA4B,mBAA5B;AAEA,MAAMC,4BAA4B,GAAG,IAAIC,GAAJ,CAAgB,CAAC,QAAD,EAAW,OAAX,CAAhB,CAArC;AAEA,eAAe,MAAMC,mBAAN,SAAkCV,YAAlC,CAA4D;AAKzEW,EAAAA,WAAW,CAACC,IAAD,EAAoB;AAC7B,UAAMA,IAAN;;AAD6B,6CAJL,IAAIH,GAAJ,EAIK;;AAAA,gDAHO,IAAII,GAAJ,EAGP;;AAAA;;AAAA,iDAeAC,KAAD,IAAyB;AACrD,UAAI,CAACR,iBAAiB,CAAC,KAAKM,IAAN,EAAY;AAAEG,QAAAA,CAAC,EAAED,KAAK,CAACE,OAAX;AAAoBC,QAAAA,CAAC,EAAEH,KAAK,CAACI;AAA7B,OAAZ,CAAtB,EAA2E;AACzE;AACD;;AAED,YAAMC,YAA0B,GAAG,KAAKC,QAAL,CAAcN,KAAd,EAAqBZ,UAAU,CAACmB,IAAhC,CAAnC;AACA,YAAMC,MAAM,GAAGR,KAAK,CAACQ,MAArB;;AAEA,UAAI,CAACd,4BAA4B,CAACe,GAA7B,CAAiCD,MAAM,CAACE,OAAxC,CAAL,EAAuD;AACrDF,QAAAA,MAAM,CAACG,iBAAP,CAAyBN,YAAY,CAACO,SAAtC;AACD;;AAED,WAAKC,cAAL,CAAoBR,YAAY,CAACO,SAAjC;AACA,WAAKE,eAAL,CAAqBC,GAArB,CAAyBV,YAAY,CAACO,SAAtC;;AAEA,UAAI,EAAE,KAAKI,qBAAP,GAA+B,CAAnC,EAAsC;AACpCX,QAAAA,YAAY,CAACY,SAAb,GAAyB7B,UAAU,CAAC8B,uBAApC;AACA,aAAKC,YAAL,CAAkBd,YAAlB;AACD,OAHD,MAGO;AACL,aAAKe,aAAL,CAAmBf,YAAnB;AACD;AACF,KApC8B;;AAAA,+CAsCFL,KAAD,IAAyB;AACnD;AACA;AACA;AACA;AACA,UAAI,KAAKgB,qBAAL,KAA+B,CAAnC,EAAsC;AACpC;AACD;;AAED,YAAMX,YAA0B,GAAG,KAAKC,QAAL,CAAcN,KAAd,EAAqBZ,UAAU,CAACiC,EAAhC,CAAnC;AACA,YAAMb,MAAM,GAAGR,KAAK,CAACQ,MAArB;;AAEA,UAAI,CAACd,4BAA4B,CAACe,GAA7B,CAAiCD,MAAM,CAACE,OAAxC,CAAL,EAAuD;AACrDF,QAAAA,MAAM,CAACc,qBAAP,CAA6BjB,YAAY,CAACO,SAA1C;AACD;;AAED,WAAKW,iBAAL,CAAuBlB,YAAY,CAACO,SAApC;AACA,WAAKE,eAAL,CAAqBU,MAArB,CAA4BnB,YAAY,CAACO,SAAzC;;AAEA,UAAI,EAAE,KAAKI,qBAAP,GAA+B,CAAnC,EAAsC;AACpCX,QAAAA,YAAY,CAACY,SAAb,GAAyB7B,UAAU,CAACqC,qBAApC;AACA,aAAKC,eAAL,CAAqBrB,YAArB;AACD,OAHD,MAGO;AACL,aAAKsB,WAAL,CAAiBtB,YAAjB;AACD;AACF,KA/D8B;;AAAA,iDAiEAL,KAAD,IAAyB;AACrD,YAAMK,YAA0B,GAAG,KAAKC,QAAL,CAAcN,KAAd,EAAqBZ,UAAU,CAACwC,IAAhC,CAAnC;AACA,YAAMpB,MAAM,GAAGR,KAAK,CAACQ,MAArB,CAFqD,CAIrD;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AACA,UACE,CAACA,MAAM,CAACqB,iBAAP,CAAyB7B,KAAK,CAACY,SAA/B,CAAD,IACA,CAAClB,4BAA4B,CAACe,GAA7B,CAAiCD,MAAM,CAACE,OAAxC,CAFH,EAGE;AACAF,QAAAA,MAAM,CAACG,iBAAP,CAAyBX,KAAK,CAACY,SAA/B;AACD;;AAED,YAAMkB,QAAiB,GAAGtC,iBAAiB,CAAC,KAAKM,IAAN,EAAY;AACrDG,QAAAA,CAAC,EAAEI,YAAY,CAACJ,CADqC;AAErDE,QAAAA,CAAC,EAAEE,YAAY,CAACF;AAFqC,OAAZ,CAA3C;AAKA,YAAM4B,YAAoB,GAAG,KAAKC,gBAAL,CAAsBC,OAAtB,CAC3B5B,YAAY,CAACO,SADc,CAA7B;;AAIA,UAAIkB,QAAJ,EAAc;AACZ,YAAIC,YAAY,GAAG,CAAnB,EAAsB;AACpB1B,UAAAA,YAAY,CAACY,SAAb,GAAyB7B,UAAU,CAAC8C,KAApC;AACA,eAAKC,cAAL,CAAoB9B,YAApB;AACA,eAAKQ,cAAL,CAAoBR,YAAY,CAACO,SAAjC;AACD,SAJD,MAIO;AACL,eAAKwB,aAAL,CAAmB/B,YAAnB;AACD;AACF,OARD,MAQO;AACL,YAAI0B,YAAY,IAAI,CAApB,EAAuB;AACrB1B,UAAAA,YAAY,CAACY,SAAb,GAAyB7B,UAAU,CAACiD,KAApC;AACA,eAAKC,cAAL,CAAoBjC,YAApB;AACA,eAAKkB,iBAAL,CAAuBlB,YAAY,CAACO,SAApC;AACD,SAJD,MAIO;AACL,eAAK2B,oBAAL,CAA0BlC,YAA1B;AACD;AACF;;AAED,WAAKmC,YAAL,CAAkBvC,CAAlB,GAAsBD,KAAK,CAACC,CAA5B;AACA,WAAKuC,YAAL,CAAkBrC,CAAlB,GAAsBH,KAAK,CAACG,CAA5B;AACD,KArH8B;;AAAA,mDAuHEH,KAAD,IAAyB;AACvD,YAAMK,YAA0B,GAAG,KAAKC,QAAL,CAAcN,KAAd,EAAqBZ,UAAU,CAACqD,MAAhC,CAAnC;AAEA,WAAKC,eAAL,CAAqBrC,YAArB;AACA,WAAKkB,iBAAL,CAAuBlB,YAAY,CAACO,SAApC;AACA,WAAKI,qBAAL,GAA6B,CAA7B;AACA,WAAKF,eAAL,CAAqB6B,KAArB;AACD,KA9H8B;;AAAA,kDAgIC3C,KAAD,IAAyB;AACtD,YAAMK,YAA0B,GAAG,KAAKC,QAAL,CAAcN,KAAd,EAAqBZ,UAAU,CAAC8C,KAAhC,CAAnC;AAEA,WAAKU,iBAAL,CAAuBvC,YAAvB;AACD,KApI8B;;AAAA,kDAsICL,KAAD,IAAyB;AACtD,YAAMK,YAA0B,GAAG,KAAKC,QAAL,CAAcN,KAAd,EAAqBZ,UAAU,CAACiD,KAAhC,CAAnC;AAEA,WAAKQ,gBAAL,CAAsBxC,YAAtB;AACD,KA1I8B;;AAAA,wDA4IOL,KAAD,IAAyB;AAC5D,YAAMK,YAA0B,GAAG,KAAKC,QAAL,CAAcN,KAAd,EAAqBZ,UAAU,CAACqD,MAAhC,CAAnC;;AAEA,UAAI,KAAK3B,eAAL,CAAqBL,GAArB,CAAyBJ,YAAY,CAACO,SAAtC,CAAJ,EAAsD;AACpD;AACA;AACA,aAAK8B,eAAL,CAAqBrC,YAArB;AAEA,aAAKW,qBAAL,GAA6B,CAA7B;AACA,aAAKF,eAAL,CAAqB6B,KAArB;AACD;AACF,KAvJ8B;;AAG7B,SAAKG,kBAAL,CAAwBC,GAAxB,CAA4B,CAA5B,EAA+B5D,WAAW,CAAC6D,IAA3C;AACA,SAAKF,kBAAL,CAAwBC,GAAxB,CAA4B,CAA5B,EAA+B5D,WAAW,CAAC8D,MAA3C;AACA,SAAKH,kBAAL,CAAwBC,GAAxB,CAA4B,CAA5B,EAA+B5D,WAAW,CAAC+D,KAA3C;AACA,SAAKJ,kBAAL,CAAwBC,GAAxB,CAA4B,CAA5B,EAA+B5D,WAAW,CAACgE,QAA3C;AACA,SAAKL,kBAAL,CAAwBC,GAAxB,CAA4B,CAA5B,EAA+B5D,WAAW,CAACiE,QAA3C;AAEA,SAAKZ,YAAL,GAAoB;AAClBvC,MAAAA,CAAC,EAAE,CAACoD,QADc;AAElBlD,MAAAA,CAAC,EAAE,CAACkD;AAFc,KAApB;AAID;;AA4IMC,EAAAA,iBAAiB,GAAS;AAC/B,SAAKxD,IAAL,CAAUyD,gBAAV,CAA2B,aAA3B,EAA0C,KAAKC,mBAA/C;AACA,SAAK1D,IAAL,CAAUyD,gBAAV,CAA2B,WAA3B,EAAwC,KAAKE,iBAA7C;AACA,SAAK3D,IAAL,CAAUyD,gBAAV,CAA2B,aAA3B,EAA0C,KAAKG,mBAA/C;AACA,SAAK5D,IAAL,CAAUyD,gBAAV,CAA2B,eAA3B,EAA4C,KAAKI,qBAAjD,EAJ+B,CAM/B;AACA;AACA;AACA;;AACA,SAAK7D,IAAL,CAAUyD,gBAAV,CAA2B,cAA3B,EAA2C,KAAKK,oBAAhD;AACA,SAAK9D,IAAL,CAAUyD,gBAAV,CAA2B,cAA3B,EAA2C,KAAKM,oBAAhD;AACA,SAAK/D,IAAL,CAAUyD,gBAAV,CACE,oBADF,EAEE,KAAKO,0BAFP;AAID;;AAEMC,EAAAA,mBAAmB,GAAS;AACjC,SAAKjE,IAAL,CAAUkE,mBAAV,CAA8B,aAA9B,EAA6C,KAAKR,mBAAlD;AACA,SAAK1D,IAAL,CAAUkE,mBAAV,CAA8B,WAA9B,EAA2C,KAAKP,iBAAhD;AACA,SAAK3D,IAAL,CAAUkE,mBAAV,CAA8B,aAA9B,EAA6C,KAAKN,mBAAlD;AACA,SAAK5D,IAAL,CAAUkE,mBAAV,CAA8B,eAA9B,EAA+C,KAAKL,qBAApD;AACA,SAAK7D,IAAL,CAAUkE,mBAAV,CAA8B,cAA9B,EAA8C,KAAKJ,oBAAnD;AACA,SAAK9D,IAAL,CAAUkE,mBAAV,CAA8B,cAA9B,EAA8C,KAAKH,oBAAnD;AACA,SAAK/D,IAAL,CAAUkE,mBAAV,CACE,oBADF,EAEE,KAAKF,0BAFP;AAID;;AAESxD,EAAAA,QAAQ,CAACN,KAAD,EAAsBiB,SAAtB,EAA2D;AAAA;;AAC3E,UAAMgD,IAAI,GAAG,KAAKnE,IAAL,CAAUoE,qBAAV,EAAb;AACA,UAAM;AAAEC,MAAAA,MAAF;AAAUC,MAAAA;AAAV,QAAqB9E,kBAAkB,CAAC,KAAKQ,IAAN,CAA7C;AAEA,WAAO;AACLG,MAAAA,CAAC,EAAED,KAAK,CAACE,OADJ;AAELC,MAAAA,CAAC,EAAEH,KAAK,CAACI,OAFJ;AAGLiE,MAAAA,OAAO,EAAE,CAACrE,KAAK,CAACE,OAAN,GAAgB+D,IAAI,CAACK,IAAtB,IAA8BH,MAHlC;AAILI,MAAAA,OAAO,EAAE,CAACvE,KAAK,CAACI,OAAN,GAAgB6D,IAAI,CAACO,GAAtB,IAA6BJ,MAJjC;AAKLxD,MAAAA,SAAS,EAAEZ,KAAK,CAACY,SALZ;AAMLK,MAAAA,SAAS,EAAEA,SANN;AAOLwD,MAAAA,WAAW,2BACTpF,kBAAkB,CAACqF,GAAnB,CAAuB1E,KAAK,CAACyE,WAA7B,CADS,yEACoChF,WAAW,CAACkF,KARtD;AASLC,MAAAA,MAAM,EAAE,KAAK9B,kBAAL,CAAwB4B,GAAxB,CAA4B1E,KAAK,CAAC4E,MAAlC,CATH;AAULC,MAAAA,IAAI,EAAE7E,KAAK,CAAC8E,SAVP;AAWLC,MAAAA,UAAU,EAAExF,oBAAoB,CAACS,KAAD;AAX3B,KAAP;AAaD;;AAEMgF,EAAAA,YAAY,GAAS;AAC1B,UAAMA,YAAN;AACA,SAAKlE,eAAL,CAAqB6B,KAArB;AACD;;AAnNwE", "sourcesContent": ["import EventManager from './EventManager';\nimport { MouseButton } from '../../handlers/gestureHandlerCommon';\nimport { AdaptedEvent, EventTypes, Point } from '../interfaces';\nimport {\n  PointerTypeMapping,\n  calculateViewScale,\n  tryExtractStylusData,\n  isPointerInBounds,\n} from '../utils';\nimport { PointerType } from '../../PointerType';\n\nconst POINTER_CAPTURE_EXCLUDE_LIST = new Set<string>(['SELECT', 'INPUT']);\n\nexport default class PointerEventManager extends EventManager<HTMLElement> {\n  private trackedPointers = new Set<number>();\n  private readonly mouseButtonsMapper = new Map<number, MouseButton>();\n  private lastPosition: Point;\n\n  constructor(view: HTMLElement) {\n    super(view);\n\n    this.mouseButtonsMapper.set(0, MouseButton.LEFT);\n    this.mouseButtonsMapper.set(1, MouseButton.MIDDLE);\n    this.mouseButtonsMapper.set(2, MouseButton.RIGHT);\n    this.mouseButtonsMapper.set(3, MouseButton.BUTTON_4);\n    this.mouseButtonsMapper.set(4, <PERSON>Button.BUTTON_5);\n\n    this.lastPosition = {\n      x: -Infinity,\n      y: -Infinity,\n    };\n  }\n\n  private pointerDownCallback = (event: PointerEvent) => {\n    if (!isPointerInBounds(this.view, { x: event.clientX, y: event.clientY })) {\n      return;\n    }\n\n    const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.DOWN);\n    const target = event.target as HTMLElement;\n\n    if (!POINTER_CAPTURE_EXCLUDE_LIST.has(target.tagName)) {\n      target.setPointerCapture(adaptedEvent.pointerId);\n    }\n\n    this.markAsInBounds(adaptedEvent.pointerId);\n    this.trackedPointers.add(adaptedEvent.pointerId);\n\n    if (++this.activePointersCounter > 1) {\n      adaptedEvent.eventType = EventTypes.ADDITIONAL_POINTER_DOWN;\n      this.onPointerAdd(adaptedEvent);\n    } else {\n      this.onPointerDown(adaptedEvent);\n    }\n  };\n\n  private pointerUpCallback = (event: PointerEvent) => {\n    // When we call reset on gesture handlers, it also resets their event managers\n    // In some handlers (like RotationGestureHandler) reset is called before all pointers leave view\n    // This means, that activePointersCounter will be set to 0, while there are still remaining pointers on view\n    // Removing them will end in activePointersCounter going below 0, therefore handlers won't behave properly\n    if (this.activePointersCounter === 0) {\n      return;\n    }\n\n    const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.UP);\n    const target = event.target as HTMLElement;\n\n    if (!POINTER_CAPTURE_EXCLUDE_LIST.has(target.tagName)) {\n      target.releasePointerCapture(adaptedEvent.pointerId);\n    }\n\n    this.markAsOutOfBounds(adaptedEvent.pointerId);\n    this.trackedPointers.delete(adaptedEvent.pointerId);\n\n    if (--this.activePointersCounter > 0) {\n      adaptedEvent.eventType = EventTypes.ADDITIONAL_POINTER_UP;\n      this.onPointerRemove(adaptedEvent);\n    } else {\n      this.onPointerUp(adaptedEvent);\n    }\n  };\n\n  private pointerMoveCallback = (event: PointerEvent) => {\n    const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.MOVE);\n    const target = event.target as HTMLElement;\n\n    // You may be wondering why are we setting pointer capture here, when we\n    // already set it in `pointerdown` handler. Well, that's a great question,\n    // for which I don't have an answer. Specification (https://www.w3.org/TR/pointerevents2/#dom-element-setpointercapture)\n    // says that the requirement for `setPointerCapture` to work is that pointer\n    // must be in 'active buttons state`, otherwise it will fail silently, which\n    // is lovely. Obviously, when `pointerdown` is fired, one of the buttons\n    // (when using mouse) is pressed, but that doesn't mean that `setPointerCapture`\n    // will succeed, for some reason. Since it fails silently, we don't actually know\n    // if it worked or not (there's `gotpointercapture` event, but the complexity of\n    // incorporating it here seems stupid), so we just call it again here, every time\n    // pointer moves until it succeeds.\n    // God, I do love web development.\n    if (\n      !target.hasPointerCapture(event.pointerId) &&\n      !POINTER_CAPTURE_EXCLUDE_LIST.has(target.tagName)\n    ) {\n      target.setPointerCapture(event.pointerId);\n    }\n\n    const inBounds: boolean = isPointerInBounds(this.view, {\n      x: adaptedEvent.x,\n      y: adaptedEvent.y,\n    });\n\n    const pointerIndex: number = this.pointersInBounds.indexOf(\n      adaptedEvent.pointerId\n    );\n\n    if (inBounds) {\n      if (pointerIndex < 0) {\n        adaptedEvent.eventType = EventTypes.ENTER;\n        this.onPointerEnter(adaptedEvent);\n        this.markAsInBounds(adaptedEvent.pointerId);\n      } else {\n        this.onPointerMove(adaptedEvent);\n      }\n    } else {\n      if (pointerIndex >= 0) {\n        adaptedEvent.eventType = EventTypes.LEAVE;\n        this.onPointerLeave(adaptedEvent);\n        this.markAsOutOfBounds(adaptedEvent.pointerId);\n      } else {\n        this.onPointerOutOfBounds(adaptedEvent);\n      }\n    }\n\n    this.lastPosition.x = event.x;\n    this.lastPosition.y = event.y;\n  };\n\n  private pointerCancelCallback = (event: PointerEvent) => {\n    const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.CANCEL);\n\n    this.onPointerCancel(adaptedEvent);\n    this.markAsOutOfBounds(adaptedEvent.pointerId);\n    this.activePointersCounter = 0;\n    this.trackedPointers.clear();\n  };\n\n  private pointerEnterCallback = (event: PointerEvent) => {\n    const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.ENTER);\n\n    this.onPointerMoveOver(adaptedEvent);\n  };\n\n  private pointerLeaveCallback = (event: PointerEvent) => {\n    const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.LEAVE);\n\n    this.onPointerMoveOut(adaptedEvent);\n  };\n\n  private lostPointerCaptureCallback = (event: PointerEvent) => {\n    const adaptedEvent: AdaptedEvent = this.mapEvent(event, EventTypes.CANCEL);\n\n    if (this.trackedPointers.has(adaptedEvent.pointerId)) {\n      // In some cases the `pointerup` event is not fired, but `lostpointercapture` is.\n      // Here we simulate the `pointercancel` event to make sure the gesture handler stops tracking it.\n      this.onPointerCancel(adaptedEvent);\n\n      this.activePointersCounter = 0;\n      this.trackedPointers.clear();\n    }\n  };\n\n  public registerListeners(): void {\n    this.view.addEventListener('pointerdown', this.pointerDownCallback);\n    this.view.addEventListener('pointerup', this.pointerUpCallback);\n    this.view.addEventListener('pointermove', this.pointerMoveCallback);\n    this.view.addEventListener('pointercancel', this.pointerCancelCallback);\n\n    // onPointerEnter and onPointerLeave are triggered by a custom logic responsible for\n    // handling shouldCancelWhenOutside flag, and are unreliable unless the pointer is down.\n    // We therefore use pointerenter and pointerleave events to handle the hover gesture,\n    // mapping them to onPointerMoveOver and onPointerMoveOut respectively.\n    this.view.addEventListener('pointerenter', this.pointerEnterCallback);\n    this.view.addEventListener('pointerleave', this.pointerLeaveCallback);\n    this.view.addEventListener(\n      'lostpointercapture',\n      this.lostPointerCaptureCallback\n    );\n  }\n\n  public unregisterListeners(): void {\n    this.view.removeEventListener('pointerdown', this.pointerDownCallback);\n    this.view.removeEventListener('pointerup', this.pointerUpCallback);\n    this.view.removeEventListener('pointermove', this.pointerMoveCallback);\n    this.view.removeEventListener('pointercancel', this.pointerCancelCallback);\n    this.view.removeEventListener('pointerenter', this.pointerEnterCallback);\n    this.view.removeEventListener('pointerleave', this.pointerLeaveCallback);\n    this.view.removeEventListener(\n      'lostpointercapture',\n      this.lostPointerCaptureCallback\n    );\n  }\n\n  protected mapEvent(event: PointerEvent, eventType: EventTypes): AdaptedEvent {\n    const rect = this.view.getBoundingClientRect();\n    const { scaleX, scaleY } = calculateViewScale(this.view);\n\n    return {\n      x: event.clientX,\n      y: event.clientY,\n      offsetX: (event.clientX - rect.left) / scaleX,\n      offsetY: (event.clientY - rect.top) / scaleY,\n      pointerId: event.pointerId,\n      eventType: eventType,\n      pointerType:\n        PointerTypeMapping.get(event.pointerType) ?? PointerType.OTHER,\n      button: this.mouseButtonsMapper.get(event.button),\n      time: event.timeStamp,\n      stylusData: tryExtractStylusData(event),\n    };\n  }\n\n  public resetManager(): void {\n    super.resetManager();\n    this.trackedPointers.clear();\n  }\n}\n"]}