var marky=function(r){"use strict";var e="undefined"!=typeof performance&&performance,n=e&&e.now?function(){return e.now()}:function(){return Date.now()};function t(r){if(!r)throw new Error("name must be non-empty")}if(r.mark=void 0,r.stop=void 0,r.getEntries=void 0,r.clear=void 0,e&&e.mark&&e.measure&&e.getEntriesByName&&e.getEntriesByType&&e.clearMarks&&e.clearMeasures)r.mark=function(r){t(r),e.mark("start "+r)},r.stop=function(r){t(r),e.mark("end "+r);var n=e.measure(r,"start "+r,"end "+r);if(n)return n;var a=e.getEntriesByName(r);return a[a.length-1]},r.getEntries=function(){return e.getEntriesByType("measure")},r.clear=function(){e.clearMark<PERSON>(),e.clearMeasures()};else{var a={},i=[];r.mark=function(r){t(r);var e=n();a["$"+r]=e},r.stop=function(r){t(r);var e=n(),o=a["$"+r];if(!o)throw new Error("no known mark: "+r);var u={startTime:o,name:r,duration:e-o,entryType:"measure"};return function(r,e){for(var n,t=0,a=r.length;t<a;)r[n=t+a>>>1].startTime<e.startTime?t=n+1:a=n;r.splice(t,0,e)}(i,u),u},r.getEntries=function(){return i},r.clear=function(){a={},i=[]}}return r}({});
