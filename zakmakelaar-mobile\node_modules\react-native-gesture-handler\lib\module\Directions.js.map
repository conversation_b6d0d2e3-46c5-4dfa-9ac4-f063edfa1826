{"version": 3, "sources": ["Directions.ts"], "names": ["RIGHT", "LEFT", "UP", "DOWN", "Directions", "DiagonalDirections", "UP_RIGHT", "DOWN_RIGHT", "UP_LEFT", "DOWN_LEFT"], "mappings": "AAAA,MAAMA,KAAK,GAAG,CAAd;AACA,MAAMC,IAAI,GAAG,CAAb;AACA,MAAMC,EAAE,GAAG,CAAX;AACA,MAAMC,IAAI,GAAG,CAAb,C,CAEA;;AACA,OAAO,MAAMC,UAAU,GAAG;AACxBJ,EAAAA,KAAK,EAAEA,KADiB;AAExBC,EAAAA,IAAI,EAAEA,IAFkB;AAGxBC,EAAAA,EAAE,EAAEA,EAHoB;AAIxBC,EAAAA,IAAI,EAAEA;AAJkB,CAAnB,C,CAOP;;AACA,OAAO,MAAME,kBAAkB,GAAG;AAChCC,EAAAA,QAAQ,EAAEJ,EAAE,GAAGF,KADiB;AAEhCO,EAAAA,UAAU,EAAEJ,IAAI,GAAGH,KAFa;AAGhCQ,EAAAA,OAAO,EAAEN,EAAE,GAAGD,IAHkB;AAIhCQ,EAAAA,SAAS,EAAEN,IAAI,GAAGF;AAJc,CAA3B,C,CAOP", "sourcesContent": ["const RIGHT = 1;\nconst LEFT = 2;\nconst UP = 4;\nconst DOWN = 8;\n\n// Public interface\nexport const Directions = {\n  RIGHT: RIGHT,\n  LEFT: LEFT,\n  UP: UP,\n  DOWN: DOWN,\n} as const;\n\n// Internal interface\nexport const DiagonalDirections = {\n  UP_RIGHT: UP | RIGHT,\n  DOWN_RIGHT: DOWN | RIGHT,\n  UP_LEFT: UP | LEFT,\n  DOWN_LEFT: DOWN | LEFT,\n} as const;\n\n// eslint-disable-next-line @typescript-eslint/no-redeclare -- backward compatibility; it can be used as a type and as a value\nexport type Directions = (typeof Directions)[keyof typeof Directions];\n// eslint-disable-next-line @typescript-eslint/no-redeclare\nexport type DiagonalDirections =\n  (typeof DiagonalDirections)[keyof typeof DiagonalDirections];\n"]}