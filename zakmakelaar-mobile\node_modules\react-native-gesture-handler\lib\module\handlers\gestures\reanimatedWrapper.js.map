{"version": 3, "sources": ["reanimatedWrapper.ts"], "names": ["tagMessage", "Reanimated", "require", "e", "undefined", "useSharedValue", "setGestureState", "console", "warn"], "mappings": ";;AAKA,SAASA,UAAT,QAA2B,aAA3B;AAMA,IAAIC,UAAJ;;AAoBA,IAAI;AACFA,EAAAA,UAAU,GAAGC,OAAO,CAAC,yBAAD,CAApB;AACD,CAFD,CAEE,OAAOC,CAAP,EAAU;AACV;AACA;AACAF,EAAAA,UAAU,GAAGG,SAAb;AACD;;AAED,IAAI,iBAACH,UAAD,wCAAC,YAAYI,cAAb,CAAJ,EAAiC;AAC/B;AACA;AACAJ,EAAAA,UAAU,GAAGG,SAAb;AACD;;AAED,IAAIH,UAAU,KAAKG,SAAf,IAA4B,CAACH,UAAU,CAACK,eAA5C,EAA6D;AAC3D;AACAL,EAAAA,UAAU,CAACK,eAAX,GAA6B,MAAM;AACjC;;AACAC,IAAAA,OAAO,CAACC,IAAR,CACER,UAAU,CACR,gGADQ,CADZ;AAKD,GAPD;AAQD;;AAED,SAASC,UAAT", "sourcesContent": ["import { ComponentClass } from 'react';\nimport {\n  GestureUpdateEvent,\n  GestureStateChangeEvent,\n} from '../gestureHandlerCommon';\nimport { tagMessage } from '../../utils';\n\nexport interface SharedValue<T> {\n  value: T;\n}\n\nlet Reanimated:\n  | {\n      default: {\n        // Slightly modified definition copied from 'react-native-reanimated'\n        // eslint-disable-next-line @typescript-eslint/ban-types\n        createAnimatedComponent<P extends object>(\n          component: ComponentClass<P>,\n          options?: unknown\n        ): ComponentClass<P>;\n      };\n      useEvent: (\n        callback: (event: GestureUpdateEvent | GestureStateChangeEvent) => void,\n        events: string[],\n        rebuild: boolean\n      ) => unknown;\n      useSharedValue: <T>(value: T) => SharedValue<T>;\n      setGestureState: (handlerTag: number, newState: number) => void;\n    }\n  | undefined;\n\ntry {\n  Reanimated = require('react-native-reanimated');\n} catch (e) {\n  // When 'react-native-reanimated' is not available we want to quietly continue\n  // @ts-ignore TS demands the variable to be initialized\n  Reanimated = undefined;\n}\n\nif (!Reanimated?.useSharedValue) {\n  // @ts-ignore Make sure the loaded module is actually Reanimated, if it's not\n  // reset the module to undefined so we can fallback to the default implementation\n  Reanimated = undefined;\n}\n\nif (Reanimated !== undefined && !Reanimated.setGestureState) {\n  // The loaded module is Reanimated but it doesn't have the setGestureState defined\n  Reanimated.setGestureState = () => {\n    'worklet';\n    console.warn(\n      tagMessage(\n        'Please use newer version of react-native-reanimated in order to control state of the gestures.'\n      )\n    );\n  };\n}\n\nexport { Reanimated };\n"]}