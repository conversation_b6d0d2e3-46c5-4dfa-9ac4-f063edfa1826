{"version": 3, "sources": ["HoverGestureHandler.ts"], "names": ["State", "GestureHandlerOrchestrator", "Gesture<PERSON>andler", "HoverGestureHandler", "transformNativeEvent", "stylusData", "onPointerMoveOver", "event", "instance", "recordHandlerIfNotPresent", "tracker", "addToTracker", "state", "UNDETERMINED", "begin", "activate", "onPointerMoveOut", "removeFromTracker", "pointerId", "end", "onPointerMove", "track", "onPointerCancel", "reset"], "mappings": ";;AAAA,SAASA,KAAT,QAAsB,aAAtB;AAEA,OAAOC,0BAAP,MAAuC,qCAAvC;AACA,OAAOC,cAAP,MAA2B,kBAA3B;AAEA,eAAe,MAAMC,mBAAN,SAAkCD,cAAlC,CAAiD;AAAA;AAAA;;AAAA;AAAA;;AAGpDE,EAAAA,oBAAoB,GAA4B;AACxD,WAAO,EACL,GAAG,MAAMA,oBAAN,EADE;AAELC,MAAAA,UAAU,EAAE,KAAKA;AAFZ,KAAP;AAID;;AAESC,EAAAA,iBAAiB,CAACC,KAAD,EAA4B;AACrDN,IAAAA,0BAA0B,CAACO,QAA3B,CAAoCC,yBAApC,CAA8D,IAA9D;AAEA,SAAKC,OAAL,CAAaC,YAAb,CAA0BJ,KAA1B;AACA,SAAKF,UAAL,GAAkBE,KAAK,CAACF,UAAxB;AACA,UAAMC,iBAAN,CAAwBC,KAAxB;;AAEA,QAAI,KAAKK,KAAL,KAAeZ,KAAK,CAACa,YAAzB,EAAuC;AACrC,WAAKC,KAAL;AACA,WAAKC,QAAL;AACD;AACF;;AAESC,EAAAA,gBAAgB,CAACT,KAAD,EAA4B;AACpD,SAAKG,OAAL,CAAaO,iBAAb,CAA+BV,KAAK,CAACW,SAArC;AACA,SAAKb,UAAL,GAAkBE,KAAK,CAACF,UAAxB;AAEA,UAAMW,gBAAN,CAAuBT,KAAvB;AAEA,SAAKY,GAAL;AACD;;AAESC,EAAAA,aAAa,CAACb,KAAD,EAA4B;AACjD,SAAKG,OAAL,CAAaW,KAAb,CAAmBd,KAAnB;AACA,SAAKF,UAAL,GAAkBE,KAAK,CAACF,UAAxB;AAEA,UAAMe,aAAN,CAAoBb,KAApB;AACD;;AAESe,EAAAA,eAAe,CAACf,KAAD,EAA4B;AACnD,UAAMe,eAAN,CAAsBf,KAAtB;AACA,SAAKgB,KAAL;AACD;;AA1C6D", "sourcesContent": ["import { State } from '../../State';\nimport { AdaptedEvent, StylusData } from '../interfaces';\nimport GestureHandlerOrchestrator from '../tools/GestureHandlerOrchestrator';\nimport GestureHandler from './GestureHandler';\n\nexport default class HoverGestureHandler extends GestureHandler {\n  private stylusData: StylusData | undefined;\n\n  protected transformNativeEvent(): Record<string, unknown> {\n    return {\n      ...super.transformNativeEvent(),\n      stylusData: this.stylusData,\n    };\n  }\n\n  protected onPointerMoveOver(event: AdaptedEvent): void {\n    GestureHandlerOrchestrator.instance.recordHandlerIfNotPresent(this);\n\n    this.tracker.addToTracker(event);\n    this.stylusData = event.stylusData;\n    super.onPointerMoveOver(event);\n\n    if (this.state === State.UNDETERMINED) {\n      this.begin();\n      this.activate();\n    }\n  }\n\n  protected onPointerMoveOut(event: AdaptedEvent): void {\n    this.tracker.removeFromTracker(event.pointerId);\n    this.stylusData = event.stylusData;\n\n    super.onPointerMoveOut(event);\n\n    this.end();\n  }\n\n  protected onPointerMove(event: AdaptedEvent): void {\n    this.tracker.track(event);\n    this.stylusData = event.stylusData;\n\n    super.onPointerMove(event);\n  }\n\n  protected onPointerCancel(event: AdaptedEvent): void {\n    super.onPointerCancel(event);\n    this.reset();\n  }\n}\n"]}