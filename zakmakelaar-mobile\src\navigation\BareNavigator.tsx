import React from "react";
import { View, Text, StyleSheet } from "react-native";
import { useAuthStore } from "@/stores/authStore";

// Import screens
import LoginScreen from "@/screens/auth/LoginScreen";
import DashboardScreen from "@/screens/dashboard/DashboardScreen";
import LoadingScreen from "@/screens/LoadingScreen";

// Completely bypass React Navigation to test if the issue is there
export default function BareNavigator() {
  const { isAuthenticated, isLoading } = useAuthStore();

  if (isLoading) {
    return <LoadingScreen />;
  }

  // Simple conditional rendering without React Navigation
  return (
    <View style={styles.container}>
      {isAuthenticated ? (
        <DashboardScreen />
      ) : (
        <LoginScreen />
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#1a1a1a",
  },
});
