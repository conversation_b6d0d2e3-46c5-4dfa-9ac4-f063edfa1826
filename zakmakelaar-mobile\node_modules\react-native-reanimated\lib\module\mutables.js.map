{"version": 3, "names": ["ReanimatedError", "logger", "shouldBeUseWeb", "isFirstReactRender", "isReactRendering", "shareableMappingCache", "makeShareableCloneRecursive", "executeOnUIRuntimeSync", "runOnUI", "valueSetter", "SHOULD_BE_USE_WEB", "shouldWarnAboutAccessDuringRender", "__DEV__", "checkInvalidReadDuringRender", "warn", "strict", "checkInvalidWriteDuringRender", "addCompilerSafeGetAndSet", "mutable", "Object", "defineProperties", "get", "value", "configurable", "enumerable", "set", "newValue", "__isAnimationDefinition", "hideInternalValueProp", "defineProperty", "makeMutableUI", "initial", "listeners", "Map", "_value", "for<PERSON>ach", "listener", "modify", "modifier", "forceUpdate", "undefined", "addListener", "id", "removeListener", "delete", "_animation", "_isReanimatedSharedValue", "makeMutableNative", "handle", "__init", "uiValueGetter", "sv", "_newValue", "makeMutableWeb", "makeMutable"], "sourceRoot": "../../src", "sources": ["mutables.ts"], "mappings": "AAAA,YAAY;;AAEZ,SAASA,eAAe,QAAQ,aAAU;AAC1C,SAASC,MAAM,QAAQ,mBAAU;AACjC,SAASC,cAAc,QAAQ,sBAAmB;AAClD,SAASC,kBAAkB,EAAEC,gBAAgB,QAAQ,iBAAc;AACnE,SAASC,qBAAqB,QAAQ,4BAAyB;AAC/D,SAASC,2BAA2B,QAAQ,iBAAc;AAC1D,SAASC,sBAAsB,EAAEC,OAAO,QAAQ,cAAW;AAC3D,SAASC,WAAW,QAAQ,kBAAe;AAE3C,MAAMC,iBAAiB,GAAGR,cAAc,CAAC,CAAC;AAE1C,SAASS,iCAAiCA,CAAA,EAAG;EAC3C,OAAOC,OAAO,IAAIR,gBAAgB,CAAC,CAAC,IAAI,CAACD,kBAAkB,CAAC,CAAC;AAC/D;AAEA,SAASU,4BAA4BA,CAAA,EAAG;EACtC,IAAIF,iCAAiC,CAAC,CAAC,EAAE;IACvCV,MAAM,CAACa,IAAI,CACT,qLAAqL,EACrL;MAAEC,MAAM,EAAE;IAAK,CACjB,CAAC;EACH;AACF;AAEA,SAASC,6BAA6BA,CAAA,EAAG;EACvC,IAAIL,iCAAiC,CAAC,CAAC,EAAE;IACvCV,MAAM,CAACa,IAAI,CACT,mLAAmL,EACnL;MAAEC,MAAM,EAAE;IAAK,CACjB,CAAC;EACH;AACF;AAMA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASE,wBAAwBA,CAAQC,OAA8B,EAAQ;EAC7E,SAAS;;EACTC,MAAM,CAACC,gBAAgB,CAACF,OAAO,EAAE;IAC/BG,GAAG,EAAE;MACHC,KAAKA,CAAA,EAAG;QACN,OAAOJ,OAAO,CAACI,KAAK;MACtB,CAAC;MACDC,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;IACd,CAAC;IACDC,GAAG,EAAE;MACHH,KAAKA,CAACI,QAA2C,EAAE;QACjD,IACE,OAAOA,QAAQ,KAAK,UAAU;QAC9B;QACA,CAAEA,QAAQ,CAA6BC,uBAAuB,EAC9D;UACAT,OAAO,CAACI,KAAK,GAAII,QAAQ,CAA6BR,OAAO,CAACI,KAAK,CAAC;QACtE,CAAC,MAAM;UACLJ,OAAO,CAACI,KAAK,GAAGI,QAAiB;QACnC;MACF,CAAC;MACDH,YAAY,EAAE,KAAK;MACnBC,UAAU,EAAE;IACd;EACF,CAAC,CAAC;AACJ;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA,SAASI,qBAAqBA,CAAQV,OAA8B,EAAE;EACpE,SAAS;;EACTC,MAAM,CAACU,cAAc,CAACX,OAAO,EAAE,QAAQ,EAAE;IACvCK,YAAY,EAAE,KAAK;IACnBC,UAAU,EAAE;EACd,CAAC,CAAC;AACJ;AAEA,OAAO,SAASM,aAAaA,CAAQC,OAAc,EAAkB;EACnE,SAAS;;EACT,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAA0B,CAAC;EACpD,IAAIX,KAAK,GAAGS,OAAO;EAEnB,MAAMb,OAA8B,GAAG;IACrC,IAAII,KAAKA,CAAA,EAAG;MACV,OAAOA,KAAK;IACd,CAAC;IACD,IAAIA,KAAKA,CAACI,QAAQ,EAAE;MAClBjB,WAAW,CAACS,OAAO,EAAoBQ,QAAQ,CAAC;IAClD,CAAC;IACD,IAAIQ,MAAMA,CAAA,EAAU;MAClB,OAAOZ,KAAK;IACd,CAAC;IACD,IAAIY,MAAMA,CAACR,QAAe,EAAE;MAC1BJ,KAAK,GAAGI,QAAQ;MAChBM,SAAS,CAACG,OAAO,CAAEC,QAAQ,IAAK;QAC9BA,QAAQ,CAACV,QAAQ,CAAC;MACpB,CAAC,CAAC;IACJ,CAAC;IACDW,MAAM,EAAEA,CAACC,QAAQ,EAAEC,WAAW,GAAG,IAAI,KAAK;MACxC9B,WAAW,CACTS,OAAO,EACPoB,QAAQ,KAAKE,SAAS,GAAGF,QAAQ,CAAChB,KAAK,CAAC,GAAGA,KAAK,EAChDiB,WACF,CAAC;IACH,CAAC;IACDE,WAAW,EAAEA,CAACC,EAAU,EAAEN,QAAyB,KAAK;MACtDJ,SAAS,CAACP,GAAG,CAACiB,EAAE,EAAEN,QAAQ,CAAC;IAC7B,CAAC;IACDO,cAAc,EAAGD,EAAU,IAAK;MAC9BV,SAAS,CAACY,MAAM,CAACF,EAAE,CAAC;IACtB,CAAC;IAEDG,UAAU,EAAE,IAAI;IAChBC,wBAAwB,EAAE;EAC5B,CAAC;EAEDlB,qBAAqB,CAACV,OAAO,CAAC;EAC9BD,wBAAwB,CAACC,OAAO,CAAC;EAEjC,OAAOA,OAAO;AAChB;AAEA,SAAS6B,iBAAiBA,CAAQhB,OAAc,EAAkB;EAChE,MAAMiB,MAAM,GAAG1C,2BAA2B,CAAC;IACzC2C,MAAM,EAAEA,CAAA,KAAM;MACZ,SAAS;;MACT,OAAOnB,aAAa,CAACC,OAAO,CAAC;IAC/B;EACF,CAAC,CAAC;EAEF,MAAMb,OAA8B,GAAG;IACrC,IAAII,KAAKA,CAAA,EAAU;MACjBT,4BAA4B,CAAC,CAAC;MAC9B,MAAMqC,aAAa,GAAG3C,sBAAsB,CAAE4C,EAAkB,IAAK;QACnE,OAAOA,EAAE,CAAC7B,KAAK;MACjB,CAAC,CAAC;MACF,OAAO4B,aAAa,CAAChC,OAAyB,CAAC;IACjD,CAAC;IACD,IAAII,KAAKA,CAACI,QAAQ,EAAE;MAClBV,6BAA6B,CAAC,CAAC;MAC/BR,OAAO,CAAC,MAAM;QACZU,OAAO,CAACI,KAAK,GAAGI,QAAQ;MAC1B,CAAC,CAAC,CAAC,CAAC;IACN,CAAC;IAED,IAAIQ,MAAMA,CAAA,EAAU;MAClB,MAAM,IAAIlC,eAAe,CACvB,sIACF,CAAC;IACH,CAAC;IACD,IAAIkC,MAAMA,CAACkB,SAAgB,EAAE;MAC3B,MAAM,IAAIpD,eAAe,CACvB,8GACF,CAAC;IACH,CAAC;IAEDqC,MAAM,EAAEA,CAACC,QAAQ,EAAEC,WAAW,GAAG,IAAI,KAAK;MACxC/B,OAAO,CAAC,MAAM;QACZU,OAAO,CAACmB,MAAM,CAACC,QAAQ,EAAEC,WAAW,CAAC;MACvC,CAAC,CAAC,CAAC,CAAC;IACN,CAAC;IACDE,WAAW,EAAEA,CAAA,KAAM;MACjB,MAAM,IAAIzC,eAAe,CACvB,sDACF,CAAC;IACH,CAAC;IACD2C,cAAc,EAAEA,CAAA,KAAM;MACpB,MAAM,IAAI3C,eAAe,CACvB,wDACF,CAAC;IACH,CAAC;IAED8C,wBAAwB,EAAE;EAC5B,CAAC;EAEDlB,qBAAqB,CAACV,OAAO,CAAC;EAC9BD,wBAAwB,CAACC,OAAO,CAAC;EAEjCb,qBAAqB,CAACoB,GAAG,CAACP,OAAO,EAAE8B,MAAM,CAAC;EAC1C,OAAO9B,OAAO;AAChB;AAEA,SAASmC,cAAcA,CAAQtB,OAAc,EAAkB;EAC7D,IAAIT,KAAY,GAAGS,OAAO;EAC1B,MAAMC,SAAS,GAAG,IAAIC,GAAG,CAA0B,CAAC;EAEpD,MAAMf,OAA8B,GAAG;IACrC,IAAII,KAAKA,CAAA,EAAU;MACjBT,4BAA4B,CAAC,CAAC;MAC9B,OAAOS,KAAK;IACd,CAAC;IACD,IAAIA,KAAKA,CAACI,QAAQ,EAAE;MAClBV,6BAA6B,CAAC,CAAC;MAC/BP,WAAW,CAACS,OAAO,EAAoBQ,QAAQ,CAAC;IAClD,CAAC;IAED,IAAIQ,MAAMA,CAAA,EAAU;MAClB,OAAOZ,KAAK;IACd,CAAC;IACD,IAAIY,MAAMA,CAACR,QAAe,EAAE;MAC1BJ,KAAK,GAAGI,QAAQ;MAChBM,SAAS,CAACG,OAAO,CAAEC,QAAQ,IAAK;QAC9BA,QAAQ,CAACV,QAAQ,CAAC;MACpB,CAAC,CAAC;IACJ,CAAC;IAEDW,MAAM,EAAEA,CAACC,QAAQ,EAAEC,WAAW,GAAG,IAAI,KAAK;MACxC9B,WAAW,CACTS,OAAO,EACPoB,QAAQ,KAAKE,SAAS,GAAGF,QAAQ,CAACpB,OAAO,CAACI,KAAK,CAAC,GAAGJ,OAAO,CAACI,KAAK,EAChEiB,WACF,CAAC;IACH,CAAC;IACDE,WAAW,EAAEA,CAACC,EAAU,EAAEN,QAAyB,KAAK;MACtDJ,SAAS,CAACP,GAAG,CAACiB,EAAE,EAAEN,QAAQ,CAAC;IAC7B,CAAC;IACDO,cAAc,EAAGD,EAAU,IAAK;MAC9BV,SAAS,CAACY,MAAM,CAACF,EAAE,CAAC;IACtB,CAAC;IAEDI,wBAAwB,EAAE;EAC5B,CAAC;EAEDlB,qBAAqB,CAACV,OAAO,CAAC;EAC9BD,wBAAwB,CAACC,OAAO,CAAC;EAEjC,OAAOA,OAAO;AAChB;AAEA,OAAO,MAAMoC,WAAW,GAAG5C,iBAAiB,GACxC2C,cAAc,GACdN,iBAAiB", "ignoreList": []}