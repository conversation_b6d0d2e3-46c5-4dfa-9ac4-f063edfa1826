{"version": 3, "sources": ["RotationGestureHandler.ts"], "names": ["RotationGestureHandler", "IndiscreteGestureHandler", "name", "NativeGestureClass", "Hammer", "Rotate", "transformNativeEvent", "rotation", "velocity", "center", "initialRotation", "DEG_RAD", "anchorX", "x", "anchorY", "y"], "mappings": ";;;;;;;AAAA;;AAEA;;AAEA;;;;AAEA,MAAMA,sBAAN,SAAqCC,iCAArC,CAA8D;AACpD,MAAJC,IAAI,GAAG;AACT,WAAO,QAAP;AACD;;AAEqB,MAAlBC,kBAAkB,GAAG;AACvB,WAAOC,kBAAOC,MAAd;AACD;;AAEDC,EAAAA,oBAAoB,CAAC;AAAEC,IAAAA,QAAF;AAAYC,IAAAA,QAAZ;AAAsBC,IAAAA;AAAtB,GAAD,EAAiD;AAAA;;AACnE,WAAO;AACLF,MAAAA,QAAQ,EAAE,CAACA,QAAQ,6BAAI,KAAKG,eAAT,yEAA4B,CAA5B,CAAT,IAA2CC,kBADhD;AAELC,MAAAA,OAAO,EAAEH,MAAM,CAACI,CAFX;AAGLC,MAAAA,OAAO,EAAEL,MAAM,CAACM,CAHX;AAILP,MAAAA;AAJK,KAAP;AAMD;;AAhB2D;;eAkB/CR,sB", "sourcesContent": ["import Hammer from '@egjs/hammerjs';\n\nimport { DEG_RAD } from './constants';\nimport { HammerInputExt } from './GestureHandler';\nimport IndiscreteGestureHandler from './IndiscreteGestureHandler';\n\nclass RotationGestureHandler extends IndiscreteGestureHandler {\n  get name() {\n    return 'rotate';\n  }\n\n  get NativeGestureClass() {\n    return Hammer.Rotate;\n  }\n\n  transformNativeEvent({ rotation, velocity, center }: HammerInputExt) {\n    return {\n      rotation: (rotation - (this.initialRotation ?? 0)) * DEG_RAD,\n      anchorX: center.x,\n      anchorY: center.y,\n      velocity,\n    };\n  }\n}\nexport default RotationGestureHandler;\n"]}