{"version": 3, "sources": ["../src/index.android.ts"], "names": [], "mappings": ";;;AAEA,IAAM,QAAA,uBAAe,GAAI,EAAA;AAElB,IAAM,YAAe,GAAA,MAC1B,mBAAoB,CAAA,GAAA,CAAI,cAAc,CAAK,IAAA;AAEhC,IAAA,uBAAA,GAA0B,CAAC,MAAoC,KAAA;AAC1E,EAAI,IAAA,OAAA,IAAW,cAAgB,EAAA;AAC7B,IAAA,MAAM,OAAU,GAAA,MAAA,CAAO,OAAQ,CAAA,MAAM,CAAE,CAAA,MAAA;AAAA,MACrC,CAAC,GAAG,KAAK,CAAA,KAAM,OAAO,KAAU,KAAA;AAAA,KAClC;AAEA,IAAM,MAAA,SAAA,GAAY,OAAQ,CAAA,IAAA,CAAK,GAAG,CAAA;AAElC,IAAA,IAAI,QAAQ,MAAS,GAAA,CAAA,IAAK,QAAS,CAAA,GAAA,CAAI,SAAS,CAAG,EAAA;AACjD,MAAA;AAAA;AAGF,IAAA,QAAA,CAAS,IAAI,SAAS,CAAA;AAEtB,IAAM,MAAA,QAAA,GAAW,QAAQ,MAAS,GAAA,CAAA;AAClC,IAAM,MAAA,SAAA,GAAY,QAAQ,MAAS,GAAA,CAAA;AAEnC,IAAA,MAAM,OAAO,OAAQ,CAAA,MAAA;AAAA,MACnB,CAAC,GAAA,EAAK,CAAC,IAAI,CAAG,EAAA,KAAA,KACZ,KAAU,KAAA,CAAA,GACN,IACA,GAAA,GAAA,IAAO,KAAU,KAAA,SAAA,GAAY,UAAU,IAAQ,CAAA,GAAA,IAAA;AAAA,MACrD;AAAA,KACF;AAEA,IAAQ,OAAA,CAAA,IAAA;AAAA,MACN,CAAG,EAAA,IAAI,CAAI,CAAA,EAAA,QAAA,GAAW,eAAe,UAAU,CAAA,6CAAA;AAAA,KACjD;AAAA;AAEJ", "file": "index.android.mjs", "sourcesContent": ["import { TurboModuleRegistry } from \"react-native\";\n\nconst warnings = new Set();\n\nexport const isEdgeToEdge = () =>\n  TurboModuleRegistry.get(\"RNEdgeToEdge\") != null;\n\nexport const controlEdgeToEdgeValues = (values: Record<string, unknown>) => {\n  if (__DEV__ && isEdgeToEdge()) {\n    const entries = Object.entries(values).filter(\n      ([, value]) => typeof value !== \"undefined\",\n    );\n\n    const stableKey = entries.join(\" \");\n\n    if (entries.length < 1 || warnings.has(stableKey)) {\n      return;\n    }\n\n    warnings.add(stableKey);\n\n    const isPlural = entries.length > 1;\n    const lastIndex = entries.length - 1;\n\n    const list = entries.reduce(\n      (acc, [name], index) =>\n        index === 0\n          ? name\n          : acc + (index === lastIndex ? \" and \" : \", \") + name,\n      \"\",\n    );\n\n    console.warn(\n      `${list} ${isPlural ? \"values are\" : \"value is\"} ignored when using react-native-edge-to-edge`,\n    );\n  }\n};\n"]}