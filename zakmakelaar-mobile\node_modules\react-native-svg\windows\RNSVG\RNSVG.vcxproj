<?xml version="1.0" encoding="utf-8"?>
<!-- This project was created with react-native-windows 0.74.9 -->
<Project ToolsVersion="Current" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <Import Project="$(SolutionDir)\ExperimentalFeatures.props" Condition="Exists('$(SolutionDir)\ExperimentalFeatures.props')" />
  <Choose>
    <When Condition="'$(UseFabric)'!='true'">
      <PropertyGroup Label="Globals">
        <CppWinRTOptimized>true</CppWinRTOptimized>
        <CppWinRTRootNamespaceAutoMerge>true</CppWinRTRootNamespaceAutoMerge>
        <MinimalCoreWin>true</MinimalCoreWin>
        <ProjectGuid>{7acf84ec-efba-4043-8e14-40b159508902}</ProjectGuid>
        <ProjectName>RNSVG</ProjectName>
        <RootNamespace>RNSVG</RootNamespace>
        <DefaultLanguage>en-US</DefaultLanguage>
        <MinimumVisualStudioVersion>17.0</MinimumVisualStudioVersion>
        <AppContainerApplication>true</AppContainerApplication>
        <ApplicationType>Windows Store</ApplicationType>
        <ApplicationTypeRevision>10.0</ApplicationTypeRevision>
      </PropertyGroup>
    </When>
    <Otherwise>
      <PropertyGroup Label="Globals">
        <CppWinRTOptimized>true</CppWinRTOptimized>
        <MinimalCoreWin>true</MinimalCoreWin>
        <ProjectGuid>{7acf84ec-efba-4043-8e14-40b159508902}</ProjectGuid>
        <ProjectName>RNSVG</ProjectName>
        <Keyword>Win32Proj</Keyword>
        <RootNamespace>RNSVG</RootNamespace>
        <WindowsTargetPlatformVersion>10.0</WindowsTargetPlatformVersion>
        <DefaultLanguage>en-US</DefaultLanguage>
        <MinimumVisualStudioVersion>17.0</MinimumVisualStudioVersion>
        <AppxPackage>false</AppxPackage>
      </PropertyGroup>
    </Otherwise>
  </Choose>
  <PropertyGroup Label="ReactNativeWindowsProps">
    <ReactNativeWindowsDir Condition="'$(ReactNativeWindowsDir)' == ''">$([MSBuild]::GetDirectoryNameOfFileAbove($(SolutionDir), 'node_modules\react-native-windows\package.json'))\node_modules\react-native-windows\</ReactNativeWindowsDir>
    <RunAutolinkCheck Condition="'$(UseFabric)'=='true'">false</RunAutolinkCheck>
  </PropertyGroup>
  <Import Project="$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.WindowsSdk.Default.props" Condition="Exists('$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.WindowsSdk.Default.props')"/>
  <PropertyGroup Label="Fallback Windows SDK Versions" Condition="'$(UseFabric)'!='true'">
    <WindowsTargetPlatformVersion Condition=" '$(WindowsTargetPlatformVersion)' == '' ">10.0.19041.0</WindowsTargetPlatformVersion>
    <WindowsTargetPlatformMinVersion Condition=" '$(WindowsTargetPlatformMinVersion)' == '' ">10.0.17763.0</WindowsTargetPlatformMinVersion>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
  <ItemGroup Label="ProjectConfigurations">
    <ProjectConfiguration Include="Debug|Win32">
      <Configuration>Debug</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|Win32">
      <Configuration>Release</Configuration>
      <Platform>Win32</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|x64">
      <Configuration>Debug</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|x64">
      <Configuration>Release</Configuration>
      <Platform>x64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Debug|ARM64">
      <Configuration>Debug</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
    <ProjectConfiguration Include="Release|ARM64">
      <Configuration>Release</Configuration>
      <Platform>ARM64</Platform>
    </ProjectConfiguration>
  </ItemGroup>
  <PropertyGroup Label="Configuration">
    <ConfigurationType>DynamicLibrary</ConfigurationType>
    <CharacterSet>Unicode</CharacterSet>
    <PlatformToolset>v143</PlatformToolset>
    <GenerateManifest>false</GenerateManifest>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)'=='Debug'" Label="Configuration">
    <UseDebugLibraries>true</UseDebugLibraries>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)'=='Release'" Label="Configuration">
    <UseDebugLibraries>false</UseDebugLibraries>
    <WholeProgramOptimization>true</WholeProgramOptimization>
  </PropertyGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
  <ImportGroup Label="ExtensionSettings">
  </ImportGroup>
  <ImportGroup Label="PropertySheets">
    <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists('$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props')" Label="LocalAppDataPlatform" />
  </ImportGroup>
  <ImportGroup Label="PropertySheets" Condition="'$(UseFabric)'!='true'">
    <Import Project="PropertySheet.props" />
  </ImportGroup>
  <ImportGroup Label="ReactNativeWindowsPropertySheets">
    <Import Project="$(ReactNativeWindowsDir)\PropertySheets\external\Microsoft.ReactNative.Uwp.CppLib.props" Condition="'$(UseFabric)'!='true' And Exists('$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Uwp.CppLib.props')" />
    <Import Project="$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Composition.CppLib.props" Condition="'$(UseFabric)'=='true' And Exists('$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Composition.CppLib.props')" />
  </ImportGroup>
  <PropertyGroup Label="UserMacros" />
  <ItemDefinitionGroup>
    <ClCompile>
      <PrecompiledHeader>Use</PrecompiledHeader>
      <PrecompiledHeaderFile>pch.h</PrecompiledHeaderFile>
      <PrecompiledHeaderOutputFile>$(IntDir)pch.pch</PrecompiledHeaderOutputFile>
      <WarningLevel>Level4</WarningLevel>
      <SDLCheck>true</SDLCheck>
      <AdditionalOptions>%(AdditionalOptions) /bigobj</AdditionalOptions>
      <DisableSpecificWarnings>4453;28204</DisableSpecificWarnings>
      <PreprocessorDefinitions>_WINRT_DLL;%(PreprocessorDefinitions)</PreprocessorDefinitions>
      <AdditionalUsingDirectories>$(WindowsSDK_WindowsMetadata);$(AdditionalUsingDirectories)</AdditionalUsingDirectories>
      <LanguageStandard>stdcpp20</LanguageStandard>
    </ClCompile>
    <Midl Condition="'$(UseFabric)'!='true'">
      <!-- This allows applications targetting older Windows SDKs (e.g. RNW 0.65 apps) to consume the library generated WinMD -->
      <AdditionalOptions>%(AdditionalOptions) /noattributename</AdditionalOptions>
    </Midl>
    <Link>
      <AdditionalDependencies>dxguid.lib;shell32.lib;user32.lib;windowsapp.lib;%(AdditionalDependenices)</AdditionalDependencies>
      <SubSystem>Console</SubSystem>
      <GenerateWindowsMetadata>true</GenerateWindowsMetadata>
      <ModuleDefinitionFile>RNSVG.def</ModuleDefinitionFile>
    </Link>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Debug'">
    <ClCompile>
      <PreprocessorDefinitions>_DEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup Condition="'$(Configuration)'=='Release'">
    <ClCompile>
      <PreprocessorDefinitions>NDEBUG;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemDefinitionGroup>
    <ClCompile>
      <PreprocessorDefinitions Condition="'$(UseFabric)'=='true'">USE_FABRIC;%(PreprocessorDefinitions)</PreprocessorDefinitions>
    </ClCompile>
  </ItemDefinitionGroup>
  <ItemGroup>
    <ClInclude Include="BrushView.h" />
    <ClInclude Include="CircleView.h" />
    <ClInclude Include="ClipPathView.h" />
    <ClInclude Include="D2DBrush.h" />
    <ClInclude Include="D2DDevice.h" />
    <ClInclude Include="D2DDeviceContext.h" />
    <ClInclude Include="D2DGeometry.h" />
    <ClInclude Include="D2DHelpers.h" />
    <ClInclude Include="DefsView.h" />
    <ClInclude Include="EllipseView.h" />
    <ClInclude Include="GroupView.h" />
    <ClInclude Include="ImageView.h" />
    <ClInclude Include="LinearGradientView.h" />
    <ClInclude Include="LineView.h" />
    <ClInclude Include="MarkerView.h" />
    <ClInclude Include="MaskView.h" />
    <ClInclude Include="PathView.h" />
    <ClInclude Include="PatternView.h" />
    <ClInclude Include="RadialGradientView.h" />
    <ClInclude Include="ReactPackageProvider.h">
      <DependentUpon>ReactPackageProvider.idl</DependentUpon>
    </ClInclude>
    <ClInclude Include="RectView.h" />
    <ClInclude Include="RenderableView.h" />
    <ClInclude Include="resource.h" />
    <ClInclude Include="RNSVGModule.h" />
    <ClInclude Include="pch.h" />
    <ClInclude Include="SVGLength.h" />
    <ClInclude Include="SvgView.h" />
    <ClInclude Include="SymbolView.h" />
    <ClInclude Include="targetver.h" Condition="'$(UseFabric)'=='true'" />
    <ClInclude Include="TextView.h" />
    <ClInclude Include="TSpanView.h" />
    <ClInclude Include="UseView.h" />
    <ClInclude Include="Utils.h" />
  </ItemGroup>
  <ItemGroup Condition="'$(UseFabric)'!='true'">
    <ClInclude Include="CircleViewManager.h" />
    <ClInclude Include="ClipPathViewManager.h" />
    <ClInclude Include="DefsViewManager.h" />
    <ClInclude Include="EllipseViewManager.h" />
    <ClInclude Include="GroupViewManager.h" />
    <ClInclude Include="ImageViewManager.h" />
    <ClInclude Include="LinearGradientViewManager.h" />
    <ClInclude Include="LineViewManager.h" />
    <ClInclude Include="MarkerViewManager.h" />
    <ClInclude Include="MaskViewManager.h" />
    <ClInclude Include="PathViewManager.h" />
    <ClInclude Include="PatternViewManager.h" />
    <ClInclude Include="RadialGradientViewManager.h" />
    <ClInclude Include="RectViewManager.h" />
    <ClInclude Include="RenderableViewManager.h" />
    <ClInclude Include="SvgViewManager.h" />
    <ClInclude Include="SymbolViewManager.h" />
    <ClInclude Include="TextViewManager.h" />
    <ClInclude Include="TSpanViewManager.h" />
    <ClInclude Include="UseViewManager.h" />
  </ItemGroup>
  <ItemGroup>
    <ClCompile Include="pch.cpp">
      <PrecompiledHeader>Create</PrecompiledHeader>
    </ClCompile>
    <ClCompile Include="ReactPackageProvider.cpp">
      <DependentUpon>ReactPackageProvider.idl</DependentUpon>
    </ClCompile>
    <ClCompile Include="$(GeneratedFilesDir)module.g.cpp" />
  </ItemGroup>
    <ItemGroup Condition="'$(UseFabric)'=='true'">
    <ClCompile Include="Fabric/CircleView.cpp" />
    <ClCompile Include="Fabric/ClipPathView.cpp" />
    <ClCompile Include="Fabric/DefsView.cpp" />
    <ClCompile Include="Fabric/EllipseView.cpp" />
    <ClCompile Include="Fabric/GroupView.cpp" />
    <ClCompile Include="Fabric/ImageView.cpp" />
    <ClCompile Include="Fabric/LinearGradientView.cpp" />
    <ClCompile Include="Fabric/LineView.cpp" />
    <ClCompile Include="Fabric/PathView.cpp" />
    <ClCompile Include="Fabric/RadialGradientView.cpp" />
    <ClCompile Include="Fabric/RectView.cpp" />
    <ClCompile Include="Fabric/RenderableView.cpp" />
    <ClCompile Include="Fabric/SvgView.cpp" />
    <ClCompile Include="Fabric/UnsupportedSvgView.cpp" />
    <ClCompile Include="Fabric/UseView.cpp" />
  </ItemGroup>
  <ItemGroup Condition="'$(UseFabric)'!='true'">
    <ClCompile Include="D2DBrush.cpp" />
    <ClCompile Include="D2DDevice.cpp" />
    <ClCompile Include="D2DDeviceContext.cpp" />
    <ClCompile Include="D2DGeometry.cpp" />
    <ClCompile Include="SVGLength.cpp" />
    <ClCompile Include="BrushView.cpp" />
    <ClCompile Include="CircleView.cpp" />
    <ClCompile Include="ClipPathView.cpp" />
    <ClCompile Include="DefsView.cpp" />
    <ClCompile Include="EllipseView.cpp" />
    <ClCompile Include="GroupView.cpp" />
    <ClCompile Include="ImageView.cpp" />
    <ClCompile Include="LinearGradientView.cpp" />
    <ClCompile Include="LineView.cpp" />
    <ClCompile Include="MarkerView.cpp" />
    <ClCompile Include="MaskView.cpp" />
    <ClCompile Include="PathView.cpp" />
    <ClCompile Include="PatternView.cpp" />
    <ClCompile Include="RadialGradientView.cpp" />
    <ClCompile Include="RectView.cpp" />
    <ClCompile Include="RenderableView.cpp" />
    <ClCompile Include="SvgView.cpp" />
    <ClCompile Include="SymbolView.cpp" />
    <ClCompile Include="TextView.cpp" />
    <ClCompile Include="TSpanView.cpp" />
    <ClCompile Include="UseView.cpp" />
    <ClCompile Include="CircleViewManager.cpp" />
    <ClCompile Include="ClipPathViewManager.cpp" />
    <ClCompile Include="DefsViewManager.cpp" />
    <ClCompile Include="EllipseViewManager.cpp" />
    <ClCompile Include="GroupViewManager.cpp" />
    <ClCompile Include="ImageViewManager.cpp" />
    <ClCompile Include="LinearGradientViewManager.cpp" />
    <ClCompile Include="LineViewManager.cpp" />
    <ClCompile Include="MarkerViewManager.cpp" />
    <ClCompile Include="MaskViewManager.cpp" />
    <ClCompile Include="PathViewManager.cpp" />
    <ClCompile Include="PatternViewManager.cpp" />
    <ClCompile Include="RadialGradientViewManager.cpp" />
    <ClCompile Include="RectViewManager.cpp" />
    <ClCompile Include="RenderableViewManager.cpp" />
    <ClCompile Include="SvgViewManager.cpp" />
    <ClCompile Include="SymbolViewManager.cpp" />
    <ClCompile Include="TextViewManager.cpp" />
    <ClCompile Include="TSpanViewManager.cpp" />
    <ClCompile Include="UseViewManager.cpp" />
  </ItemGroup>
  <ItemGroup>
    <Midl Include="Paper.idl" Condition="'$(UseFabric)'!='true'" />
    <Midl Include="ReactPackageProvider.idl" />
    <Midl Include="Types.idl" Condition="'$(UseFabric)'!='true'"/>
    <Midl Include="ViewManagers.idl" Condition="'$(UseFabric)'!='true'" />
    <Midl Include="Views.idl" Condition="'$(UseFabric)'!='true'"/>
  </ItemGroup>
  <ItemGroup Condition="'$(UseFabric)'!='true'">
    <None Include="PropertySheet.props" />
  </ItemGroup>
  <ItemGroup>
    <ResourceCompile Include="RNSVG.rc" />
  </ItemGroup>
  <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
  <ImportGroup Label="ReactNativeWindowsTargets">
    <Import Project="$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Uwp.CppLib.targets" Condition="'$(UseFabric)'!='true' And Exists('$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Uwp.CppLib.targets')" />
    <Import Project="$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Composition.CppLib.targets" Condition="'$(UseFabric)'=='true' And Exists('$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Composition.CppLib.targets')" />
  </ImportGroup>
  <Target Name="EnsureReactNativeWindowsTargets" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references targets in your node_modules\react-native-windows folder that are missing. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="'$(UseFabric)'!='true' And !Exists('$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Uwp.CppLib.props')" Text="$([System.String]::Format('$(ErrorText)', '$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Uwp.CppLib.props'))" />
    <Error Condition="'$(UseFabric)'!='true' And !Exists('$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Uwp.CppLib.targets')" Text="$([System.String]::Format('$(ErrorText)', '$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Uwp.CppLib.targets'))" />
    <Error Condition="'$(UseFabric)'=='true' And !Exists('$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Composition.CppLib.props')" Text="$([System.String]::Format('$(ErrorText)', '$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Composition.CppLib.props'))" />
    <Error Condition="'$(UseFabric)'=='true' And !Exists('$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Composition.CppLib.targets')" Text="$([System.String]::Format('$(ErrorText)', '$(ReactNativeWindowsDir)\PropertySheets\External\Microsoft.ReactNative.Composition.CppLib.targets'))" />
  </Target>
</Project>