{"version": 3, "sources": ["DraggingGestureHandler.ts"], "names": ["DraggingGestureHandler", "Gesture<PERSON>andler", "shouldEnableGestureOnSetup", "transformNativeEvent", "deltaX", "deltaY", "velocityX", "velocityY", "center", "x", "y", "rect", "view", "getBoundingClientRect", "ratio", "PixelRatio", "get", "translationX", "__initialX", "translationY", "__initialY", "absoluteX", "absoluteY", "left", "top"], "mappings": ";;;;;;;AAEA;;AACA;;;;AAHA;;AACA;AAIA,MAAeA,sBAAf,SAA8CC,uBAA9C,CAA6D;AAC7B,MAA1BC,0BAA0B,GAAG;AAC/B,WAAO,IAAP;AACD;;AAEDC,EAAAA,oBAAoB,CAAC;AACnBC,IAAAA,MADmB;AAEnBC,IAAAA,MAFmB;AAGnBC,IAAAA,SAHmB;AAInBC,IAAAA,SAJmB;AAKnBC,IAAAA,MAAM,EAAE;AAAEC,MAAAA,CAAF;AAAKC,MAAAA;AAAL;AALW,GAAD,EAMD;AACjB;AACA,UAAMC,IAAI,GAAG,KAAKC,IAAL,CAAWC,qBAAX,EAAb;;AACA,UAAMC,KAAK,GAAGC,wBAAWC,GAAX,EAAd;;AACA,WAAO;AACLC,MAAAA,YAAY,EAAEb,MAAM,IAAI,KAAKc,UAAL,IAAmB,CAAvB,CADf;AAELC,MAAAA,YAAY,EAAEd,MAAM,IAAI,KAAKe,UAAL,IAAmB,CAAvB,CAFf;AAGLC,MAAAA,SAAS,EAAEZ,CAHN;AAILa,MAAAA,SAAS,EAAEZ,CAJN;AAKLJ,MAAAA,SAAS,EAAEA,SAAS,GAAGQ,KALlB;AAMLP,MAAAA,SAAS,EAAEA,SAAS,GAAGO,KANlB;AAOLL,MAAAA,CAAC,EAAEA,CAAC,GAAGE,IAAI,CAACY,IAPP;AAQLb,MAAAA,CAAC,EAAEA,CAAC,GAAGC,IAAI,CAACa;AARP,KAAP;AAUD;;AAzB0D;;eA4B9CxB,sB", "sourcesContent": ["/* eslint-disable eslint-comments/no-unlimited-disable */\n/* eslint-disable */\nimport GestureHand<PERSON>, { HammerInputExt } from './GestureHandler';\nimport { PixelRatio } from 'react-native';\n\nabstract class DraggingGestureHandler extends GestureHandler {\n  get shouldEnableGestureOnSetup() {\n    return true;\n  }\n\n  transformNativeEvent({\n    deltaX,\n    deltaY,\n    velocityX,\n    velocityY,\n    center: { x, y },\n  }: HammerInputExt) {\n    // @ts-ignore FIXME(TS)\n    const rect = this.view!.getBoundingClientRect();\n    const ratio = PixelRatio.get();\n    return {\n      translationX: deltaX - (this.__initialX || 0),\n      translationY: deltaY - (this.__initialY || 0),\n      absoluteX: x,\n      absoluteY: y,\n      velocityX: velocityX * ratio,\n      velocityY: velocityY * ratio,\n      x: x - rect.left,\n      y: y - rect.top,\n    };\n  }\n}\n\nexport default DraggingGestureHandler;\n"]}