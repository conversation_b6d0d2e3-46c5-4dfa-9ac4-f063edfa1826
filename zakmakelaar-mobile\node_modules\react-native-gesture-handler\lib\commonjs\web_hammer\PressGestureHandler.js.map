{"version": 3, "sources": ["PressGestureHandler.ts"], "names": ["PressGestureHandler", "DiscreteGestureHandler", "name", "minDurationMs", "config", "maxDist", "NativeGestureClass", "Hammer", "Press", "simulateCancelEvent", "inputData", "hasGestureFailed", "cancelEvent", "updateHasCustomActivationCriteria", "shouldCancelWhenOutside", "maxDistSq", "getState", "type", "INPUT_START", "State", "BEGAN", "INPUT_MOVE", "ACTIVE", "INPUT_END", "END", "INPUT_CANCEL", "CANCELLED", "getConfig", "hasCustomActivationCriteria", "getHammerConfig", "time", "onGestureActivated", "ev", "onGestureStart", "shouldDelayTouchForEvent", "pointerType", "shouldDelayTouches", "isGestureRunning", "clearTimeout", "visualFeedbackTimer", "initialEvent", "sendGestureStartedEvent", "CONTENT_TOUCHES_DELAY", "sendEvent", "eventType", "<PERSON><PERSON><PERSON><PERSON>", "forceInvalidate", "event", "onRawEvent", "isFinal", "timeout", "CONTENT_TOUCHES_QUICK_TAP_END_DELAY", "onGestureEnded", "updateGestureConfig", "shouldActivateOnStart", "disallowInterruption", "Number", "NaN", "minPointers", "maxPointers", "props"], "mappings": ";;;;;;;AAAA;;AAEA;;AACA;;AAKA;;AAEA;;;;;;AAEA,MAAMA,mBAAN,SAAkCC,+BAAlC,CAAyD;AAAA;AAAA;;AAAA;;AAAA,0CAET,IAFS;;AAAA,gDAoBlC,IApBkC;AAAA;;AAG/C,MAAJC,IAAI,GAAG;AACT,WAAO,OAAP;AACD;;AAEgB,MAAbC,aAAa,GAAG;AAClB;AACA,WAAO,kBAAM,KAAKC,MAAL,CAAYD,aAAlB,IAAmC,CAAnC,GAAuC,KAAKC,MAAL,CAAYD,aAA1D;AACD;;AAEU,MAAPE,OAAO,GAAG;AACZ,WAAO,kBAAM,KAAKD,MAAL,CAAYC,OAAlB,IAA6B,CAA7B,GAAiC,KAAKD,MAAL,CAAYC,OAApD;AACD;;AAEqB,MAAlBC,kBAAkB,GAAG;AACvB,WAAOC,kBAAOC,KAAd;AACD;;AAIDC,EAAAA,mBAAmB,CAACC,SAAD,EAA4B;AAC7C;AACA,SAAKC,gBAAL,GAAwB,IAAxB;AACA,SAAKC,WAAL,CAAiBF,SAAjB;AACD;;AAEDG,EAAAA,iCAAiC,CAAC;AAChCC,IAAAA,uBADgC;AAEhCC,IAAAA;AAFgC,GAAD,EAGiB;AAChD,WAAOD,uBAAuB,IAAI,CAAC,0BAAcC,SAAd,CAAnC;AACD;;AAEDC,EAAAA,QAAQ,CAACC,IAAD,EAA6C;AACnD,WAAO;AACL,OAACV,kBAAOW,WAAR,GAAsBC,aAAMC,KADvB;AAEL,OAACb,kBAAOc,UAAR,GAAqBF,aAAMG,MAFtB;AAGL,OAACf,kBAAOgB,SAAR,GAAoBJ,aAAMK,GAHrB;AAIL,OAACjB,kBAAOkB,YAAR,GAAuBN,aAAMO;AAJxB,MAKLT,IALK,CAAP;AAMD;;AAEDU,EAAAA,SAAS,GAAG;AACV,QAAI,CAAC,KAAKC,2BAAV,EAAuC;AACrC;AACA;AACA,aAAO;AACLd,QAAAA,uBAAuB,EAAE,IADpB;AAELC,QAAAA,SAAS,EAAE;AAFN,OAAP;AAID;;AACD,WAAO,KAAKX,MAAZ;AACD;;AAEDyB,EAAAA,eAAe,GAAG;AAChB,WAAO,EACL,GAAG,MAAMA,eAAN,EADE;AAEL;AACAC,MAAAA,IAAI,EAAE,KAAK3B;AAHN,KAAP;AAKD;;AAED4B,EAAAA,kBAAkB,CAACC,EAAD,EAAqB;AACrC,SAAKC,cAAL,CAAoBD,EAApB;AACD;;AAEDE,EAAAA,wBAAwB,CAAC;AAAEC,IAAAA;AAAF,GAAD,EAAkC;AACxD;AACA,WAAO,KAAKC,kBAAL,IAA2BD,WAAW,KAAK,OAAlD;AACD;;AAEDF,EAAAA,cAAc,CAACD,EAAD,EAAqB;AACjC,SAAKK,gBAAL,GAAwB,IAAxB;AACAC,IAAAA,YAAY,CAAC,KAAKC,mBAAN,CAAZ;AACA,SAAKC,YAAL,GAAoBR,EAApB;AACA,SAAKO,mBAAL,GAA2B,8BACzB,MAAM;AACJ,WAAKE,uBAAL,CAA6B,KAAKD,YAAlC;AACA,WAAKA,YAAL,GAAoB,IAApB;AACD,KAJwB,EAKzB,KAAKN,wBAAL,CAA8BF,EAA9B,KAAqCU,gCALZ,CAA3B;AAOD;;AAEDD,EAAAA,uBAAuB,CAACT,EAAD,EAAqB;AAC1CM,IAAAA,YAAY,CAAC,KAAKC,mBAAN,CAAZ;AACA,SAAKA,mBAAL,GAA2B,IAA3B;AACA,SAAKI,SAAL,CAAe,EACb,GAAGX,EADU;AAEbY,MAAAA,SAAS,EAAErC,kBAAOc,UAFL;AAGbwB,MAAAA,OAAO,EAAE;AAHI,KAAf;AAKD;;AAEDC,EAAAA,eAAe,CAACC,KAAD,EAAwB;AACrC,UAAMD,eAAN,CAAsBC,KAAtB;AACAT,IAAAA,YAAY,CAAC,KAAKC,mBAAN,CAAZ;AACA,SAAKA,mBAAL,GAA2B,IAA3B;AACA,SAAKC,YAAL,GAAoB,IAApB;AACD;;AAEDQ,EAAAA,UAAU,CAAChB,EAAD,EAAqB;AAC7B,UAAMgB,UAAN,CAAiBhB,EAAjB;;AACA,QAAI,KAAKK,gBAAT,EAA2B;AACzB,UAAIL,EAAE,CAACiB,OAAP,EAAgB;AACd,YAAIC,OAAJ;;AACA,YAAI,KAAKX,mBAAT,EAA8B;AAC5B;AACA;AACA;AACAW,UAAAA,OAAO,GAAGC,8CAAV;AACA,eAAKV,uBAAL,CAA6B,KAAKD,YAAlC;AACA,eAAKA,YAAL,GAAoB,IAApB;AACD;;AACD,sCAAkB,MAAM;AACtB,eAAKG,SAAL,CAAe,EACb,GAAGX,EADU;AAEbY,YAAAA,SAAS,EAAErC,kBAAOgB,SAFL;AAGb0B,YAAAA,OAAO,EAAE;AAHI,WAAf,EADsB,CAMtB;;AACA,eAAKG,cAAL;AACD,SARD,EAQGF,OARH;AASD,OAnBD,MAmBO;AACL,aAAKP,SAAL,CAAe,EACb,GAAGX,EADU;AAEbY,UAAAA,SAAS,EAAErC,kBAAOc,UAFL;AAGb4B,UAAAA,OAAO,EAAE;AAHI,SAAf;AAKD;AACF;AACF;;AAEDI,EAAAA,mBAAmB,CAAC;AAClBC,IAAAA,qBAAqB,GAAG,KADN;AAElBC,IAAAA,oBAAoB,GAAG,KAFL;AAGlBzC,IAAAA,uBAAuB,GAAG,IAHR;AAIlBX,IAAAA,aAAa,GAAGqD,MAAM,CAACC,GAJL;AAKlBpD,IAAAA,OAAO,GAAGmD,MAAM,CAACC,GALC;AAMlBC,IAAAA,WAAW,GAAG,CANI;AAOlBC,IAAAA,WAAW,GAAG,CAPI;AAQlB,OAAGC;AARe,GAAD,EAShB;AACD,WAAO,MAAMP,mBAAN,CAA0B;AAC/BC,MAAAA,qBAD+B;AAE/BC,MAAAA,oBAF+B;AAG/BzC,MAAAA,uBAH+B;AAI/BX,MAAAA,aAJ+B;AAK/BE,MAAAA,OAL+B;AAM/BqD,MAAAA,WAN+B;AAO/BC,MAAAA,WAP+B;AAQ/B,SAAGC;AAR4B,KAA1B,CAAP;AAUD;;AA3JsD;;eA6J1C5D,mB", "sourcesContent": ["import Hammer from '@egjs/hammerjs';\n\nimport { State } from '../State';\nimport {\n  CONTENT_TOUCHES_DELAY,\n  CONTENT_TOUCHES_QUICK_TAP_END_DELAY,\n  HammerInputNames,\n} from './constants';\nimport DiscreteGestureHandler from './DiscreteGestureHandler';\nimport { Config, HammerInputExt } from './GestureHandler';\nimport { fireAfterInterval, isValidNumber, isnan } from './utils';\n\nclass PressGestureHandler extends DiscreteGestureHandler {\n  private visualFeedbackTimer: any;\n  private initialEvent: HammerInputExt | null = null;\n  get name() {\n    return 'press';\n  }\n\n  get minDurationMs() {\n    // @ts-ignore FIXME(TS)\n    return isnan(this.config.minDurationMs) ? 5 : this.config.minDurationMs;\n  }\n\n  get maxDist() {\n    return isnan(this.config.maxDist) ? 9 : this.config.maxDist;\n  }\n\n  get NativeGestureClass() {\n    return Hammer.Press;\n  }\n\n  shouldDelayTouches = true;\n\n  simulateCancelEvent(inputData: HammerInputExt) {\n    // Long press never starts so we can't rely on the running event boolean.\n    this.hasGestureFailed = true;\n    this.cancelEvent(inputData);\n  }\n\n  updateHasCustomActivationCriteria({\n    shouldCancelWhenOutside,\n    maxDistSq,\n  }: Config & { shouldCancelWhenOutside: boolean }) {\n    return shouldCancelWhenOutside || !isValidNumber(maxDistSq);\n  }\n\n  getState(type: keyof typeof HammerInputNames): State {\n    return {\n      [Hammer.INPUT_START]: State.BEGAN,\n      [Hammer.INPUT_MOVE]: State.ACTIVE,\n      [Hammer.INPUT_END]: State.END,\n      [Hammer.INPUT_CANCEL]: State.CANCELLED,\n    }[type];\n  }\n\n  getConfig() {\n    if (!this.hasCustomActivationCriteria) {\n      // Default config\n      // If no params have been defined then this config should emulate the native gesture as closely as possible.\n      return {\n        shouldCancelWhenOutside: true,\n        maxDistSq: 10,\n      };\n    }\n    return this.config;\n  }\n\n  getHammerConfig() {\n    return {\n      ...super.getHammerConfig(),\n      // threshold: this.maxDist,\n      time: this.minDurationMs,\n    };\n  }\n\n  onGestureActivated(ev: HammerInputExt) {\n    this.onGestureStart(ev);\n  }\n\n  shouldDelayTouchForEvent({ pointerType }: HammerInputExt) {\n    // Don't disable event for mouse input\n    return this.shouldDelayTouches && pointerType === 'touch';\n  }\n\n  onGestureStart(ev: HammerInputExt) {\n    this.isGestureRunning = true;\n    clearTimeout(this.visualFeedbackTimer);\n    this.initialEvent = ev;\n    this.visualFeedbackTimer = fireAfterInterval(\n      () => {\n        this.sendGestureStartedEvent(this.initialEvent as HammerInputExt);\n        this.initialEvent = null;\n      },\n      this.shouldDelayTouchForEvent(ev) && CONTENT_TOUCHES_DELAY\n    );\n  }\n\n  sendGestureStartedEvent(ev: HammerInputExt) {\n    clearTimeout(this.visualFeedbackTimer);\n    this.visualFeedbackTimer = null;\n    this.sendEvent({\n      ...ev,\n      eventType: Hammer.INPUT_MOVE,\n      isFirst: true,\n    });\n  }\n\n  forceInvalidate(event: HammerInputExt) {\n    super.forceInvalidate(event);\n    clearTimeout(this.visualFeedbackTimer);\n    this.visualFeedbackTimer = null;\n    this.initialEvent = null;\n  }\n\n  onRawEvent(ev: HammerInputExt) {\n    super.onRawEvent(ev);\n    if (this.isGestureRunning) {\n      if (ev.isFinal) {\n        let timeout;\n        if (this.visualFeedbackTimer) {\n          // Aesthetic timing for a quick tap.\n          // We haven't activated the tap right away to emulate iOS `delaysContentTouches`\n          // Now we must send the initial activation event and wait a set amount of time before firing the end event.\n          timeout = CONTENT_TOUCHES_QUICK_TAP_END_DELAY;\n          this.sendGestureStartedEvent(this.initialEvent as HammerInputExt);\n          this.initialEvent = null;\n        }\n        fireAfterInterval(() => {\n          this.sendEvent({\n            ...ev,\n            eventType: Hammer.INPUT_END,\n            isFinal: true,\n          });\n          // @ts-ignore -- this should explicitly support undefined\n          this.onGestureEnded();\n        }, timeout);\n      } else {\n        this.sendEvent({\n          ...ev,\n          eventType: Hammer.INPUT_MOVE,\n          isFinal: false,\n        });\n      }\n    }\n  }\n\n  updateGestureConfig({\n    shouldActivateOnStart = false,\n    disallowInterruption = false,\n    shouldCancelWhenOutside = true,\n    minDurationMs = Number.NaN,\n    maxDist = Number.NaN,\n    minPointers = 1,\n    maxPointers = 1,\n    ...props\n  }) {\n    return super.updateGestureConfig({\n      shouldActivateOnStart,\n      disallowInterruption,\n      shouldCancelWhenOutside,\n      minDurationMs,\n      maxDist,\n      minPointers,\n      maxPointers,\n      ...props,\n    });\n  }\n}\nexport default PressGestureHandler;\n"]}