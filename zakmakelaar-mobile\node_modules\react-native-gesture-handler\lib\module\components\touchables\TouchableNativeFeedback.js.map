{"version": 3, "sources": ["TouchableNativeFeedback.tsx"], "names": ["TouchableNativeFeedback", "RNTouchableNativeFeedback"], "mappings": "AAAA,SAASA,uBAAuB,IAAIC,yBAApC,QAAqE,cAArE;AAEA;AACA;AACA;;AACA,MAAMD,uBAAuB,GAAGC,yBAAhC;AAEA,eAAeD,uBAAf", "sourcesContent": ["import { TouchableNativeFeedback as RNTouchableNativeFeedback } from 'react-native';\n\n/**\n * @deprecated TouchableNativeFeedback will be removed in the future version of Gesture Handler. Use Pressable instead.\n */\nconst TouchableNativeFeedback = RNTouchableNativeFeedback;\n\nexport default TouchableNativeFeedback;\n"]}