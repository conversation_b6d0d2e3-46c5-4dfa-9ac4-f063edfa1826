// Utility to test API connectivity
import { apiClient } from "@/services/api/client";
import { getCurrentConfig } from "@/config/api";

export const testApiConnection = async (): Promise<{
  success: boolean;
  message: string;
  details?: any;
}> => {
  try {
    console.log("Testing API connection...");

    // Test basic connectivity with health check
    const config = getCurrentConfig();
    const response = await fetch(config.healthUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
      },
    });

    if (!response.ok) {
      return {
        success: false,
        message: `Health check failed: HTTP ${response.status}`,
        details: { status: response.status, statusText: response.statusText },
      };
    }

    const data = await response.json();

    return {
      success: true,
      message: "API connection successful",
      details: data,
    };
  } catch (error) {
    console.error("API connection test failed:", error);

    if (
      error instanceof TypeError &&
      error.message.includes("Network request failed")
    ) {
      return {
        success: false,
        message:
          "Cannot reach the server. Please check:\n1. Backend is running on port 3000\n2. Your device is on the same network\n3. Firewall is not blocking the connection",
        details: { error: error.message },
      };
    }

    return {
      success: false,
      message: `Connection test failed: ${error instanceof Error ? error.message : "Unknown error"}`,
      details: { error },
    };
  }
};

export const getNetworkInfo = () => {
  try {
    const Constants = require("expo-constants").default;
    return {
      debuggerHost: Constants.manifest?.debuggerHost,
      expoVersion: Constants.expoVersion,
      platform: Constants.platform,
    };
  } catch (error) {
    return {
      error: "Could not get network info",
      details: error,
    };
  }
};
