{"version": 3, "sources": ["useDetectorUpdater.ts"], "names": ["useCallback", "attachHandlers", "updateHandlers", "needsToReattach", "dropHandlers", "useForceRender", "validateDetectorChildren", "findNodeHandle", "useDetectorUpdater", "state", "preparedGesture", "gestures<PERSON>oAtta<PERSON>", "gestureConfig", "webEventHandlersRef", "forceRender", "updateAttachedGestures", "skipConfigUpdate", "viewTag", "viewRef", "didUnderlyingViewChange", "previousViewTag", "forceRebuildReanimatedEvent"], "mappings": "AAAA,SAAgBA,WAAhB,QAAmC,OAAnC;AASA,SAASC,cAAT,QAA+B,kBAA/B;AACA,SAASC,cAAT,QAA+B,kBAA/B;AACA,SAASC,eAAT,QAAgC,mBAAhC;AACA,SAASC,YAAT,QAA6B,gBAA7B;AACA,SAASC,cAAT,EAAyBC,wBAAzB,QAAyD,SAAzD;AACA,OAAOC,cAAP,MAA2B,yBAA3B,C,CAEA;AACA;AACA;;AACA,OAAO,SAASC,kBAAT,CACLC,KADK,EAELC,eAFK,EAGLC,gBAHK,EAILC,aAJK,EAKLC,mBALK,EAML;AACA,QAAMC,WAAW,GAAGT,cAAc,EAAlC;AACA,QAAMU,sBAAsB,GAAGf,WAAW,EACxC;AACCgB,EAAAA,gBAAD,IAAgC;AAC9B;AACA,UAAMC,OAAO,GAAGV,cAAc,CAACE,KAAK,CAACS,OAAP,CAA9B;AACA,UAAMC,uBAAuB,GAAGF,OAAO,KAAKR,KAAK,CAACW,eAAlD;;AAEA,QACED,uBAAuB,IACvBhB,eAAe,CAACO,eAAD,EAAkBC,gBAAlB,CAFjB,EAGE;AACAL,MAAAA,wBAAwB,CAACG,KAAK,CAACS,OAAP,CAAxB;AACAd,MAAAA,YAAY,CAACM,eAAD,CAAZ;AACAT,MAAAA,cAAc,CAAC;AACbS,QAAAA,eADa;AAEbE,QAAAA,aAFa;AAGbD,QAAAA,gBAHa;AAIbE,QAAAA,mBAJa;AAKbI,QAAAA;AALa,OAAD,CAAd;;AAQA,UAAIE,uBAAJ,EAA6B;AAC3BV,QAAAA,KAAK,CAACW,eAAN,GAAwBH,OAAxB;AACAR,QAAAA,KAAK,CAACY,2BAAN,GAAoC,IAApC;AACAP,QAAAA,WAAW;AACZ;AACF,KAnBD,MAmBO,IAAI,CAACE,gBAAL,EAAuB;AAC5Bd,MAAAA,cAAc,CAACQ,eAAD,EAAkBE,aAAlB,EAAiCD,gBAAjC,CAAd;AACD;AACF,GA7BuC,EA8BxC,CACEG,WADF,EAEEF,aAFF,EAGED,gBAHF,EAIED,eAJF,EAKED,KALF,EAMEI,mBANF,CA9BwC,CAA1C;AAwCA,SAAOE,sBAAP;AACD", "sourcesContent": ["import React, { useCallback } from 'react';\nimport { GestureType } from '../gesture';\nimport { ComposedGesture } from '../gestureComposition';\n\nimport {\n  AttachedGestureState,\n  GestureDetectorState,\n  WebEventHandler,\n} from './types';\nimport { attachHandlers } from './attachHandlers';\nimport { updateHandlers } from './updateHandlers';\nimport { needsToReattach } from './needsToReattach';\nimport { dropHandlers } from './dropHandlers';\nimport { useForceRender, validateDetectorChildren } from './utils';\nimport findNodeHandle from '../../../findNodeHandle';\n\n// Returns a function that's responsible for updating the attached gestures\n// If the view has changed, it will reattach the handlers to the new view\n// If the view remains the same, it will update the handlers with the new config\nexport function useDetectorUpdater(\n  state: GestureDetectorState,\n  preparedGesture: AttachedGestureState,\n  gesturesToAttach: GestureType[],\n  gestureConfig: ComposedGesture | GestureType,\n  webEventHandlersRef: React.RefObject<WebEventHandler>\n) {\n  const forceRender = useForceRender();\n  const updateAttachedGestures = useCallback(\n    // skipConfigUpdate is used to prevent unnecessary updates when only checking if the view has changed\n    (skipConfigUpdate?: boolean) => {\n      // If the underlying view has changed we need to reattach handlers to the new view\n      const viewTag = findNodeHandle(state.viewRef) as number;\n      const didUnderlyingViewChange = viewTag !== state.previousViewTag;\n\n      if (\n        didUnderlyingViewChange ||\n        needsToReattach(preparedGesture, gesturesToAttach)\n      ) {\n        validateDetectorChildren(state.viewRef);\n        dropHandlers(preparedGesture);\n        attachHandlers({\n          preparedGesture,\n          gestureConfig,\n          gesturesToAttach,\n          webEventHandlersRef,\n          viewTag,\n        });\n\n        if (didUnderlyingViewChange) {\n          state.previousViewTag = viewTag;\n          state.forceRebuildReanimatedEvent = true;\n          forceRender();\n        }\n      } else if (!skipConfigUpdate) {\n        updateHandlers(preparedGesture, gestureConfig, gesturesToAttach);\n      }\n    },\n    [\n      forceRender,\n      gestureConfig,\n      gesturesToAttach,\n      preparedGesture,\n      state,\n      webEventHandlersRef,\n    ]\n  );\n\n  return updateAttachedGestures;\n}\n"]}