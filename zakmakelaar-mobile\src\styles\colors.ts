// Color palette for the Zakmakelaar AI mobile app

export const Colors = {
  // Primary colors
  primary: '#007AFF',
  primaryDark: '#0056CC',
  primaryLight: '#4DA3FF',
  
  // Secondary colors
  secondary: '#FF9500',
  secondaryDark: '#CC7700',
  secondaryLight: '#FFB84D',
  
  // Status colors
  success: '#34C759',
  successDark: '#2AA946',
  successLight: '#5DD470',
  
  error: '#FF3B30',
  errorDark: '#CC2F26',
  errorLight: '#FF6B62',
  
  warning: '#FF9500',
  warningDark: '#CC7700',
  warningLight: '#FFB84D',
  
  info: '#007AFF',
  infoDark: '#0056CC',
  infoLight: '#4DA3FF',
  
  // Neutral colors (Dark theme)
  background: '#1a1a1a',
  surface: '#2a2a2a',
  surfaceLight: '#3a3a3a',
  surfaceDark: '#1a1a1a',
  
  // Text colors
  text: '#ffffff',
  textSecondary: '#8E8E93',
  textTertiary: '#6D6D70',
  textInverse: '#000000',
  
  // Border colors
  border: '#3a3a3a',
  borderLight: '#4a4a4a',
  borderDark: '#2a2a2a',
  
  // Overlay colors
  overlay: 'rgba(0, 0, 0, 0.5)',
  overlayLight: 'rgba(0, 0, 0, 0.3)',
  overlayDark: 'rgba(0, 0, 0, 0.7)',
  
  // Transparent
  transparent: 'transparent',
  
  // White and black
  white: '#ffffff',
  black: '#000000',
} as const;

// Semantic color mappings
export const SemanticColors = {
  // Agent status colors
  agentActive: Colors.success,
  agentInactive: Colors.error,
  agentPending: Colors.warning,
  
  // Application status colors
  applicationApproved: Colors.success,
  applicationRejected: Colors.error,
  applicationPending: Colors.warning,
  applicationSubmitted: Colors.info,
  
  // Priority colors
  priorityHigh: Colors.error,
  priorityMedium: Colors.warning,
  priorityLow: Colors.info,
  
  // Property type colors
  propertyHouse: '#8B5CF6',
  propertyApartment: '#06B6D4',
  propertyRoom: '#F59E0B',
  propertyStudio: '#EF4444',
  
  // Navigation colors
  tabActive: Colors.primary,
  tabInactive: Colors.textSecondary,
  
  // Form colors
  inputBackground: Colors.surface,
  inputBorder: Colors.border,
  inputFocus: Colors.primary,
  inputError: Colors.error,
  
  // Button colors
  buttonPrimary: Colors.primary,
  buttonSecondary: Colors.surface,
  buttonDanger: Colors.error,
  buttonSuccess: Colors.success,
} as const;

// Theme object for easy consumption
export const Theme = {
  colors: Colors,
  semantic: SemanticColors,
  
  // Common color combinations
  card: {
    background: Colors.surface,
    border: Colors.border,
    text: Colors.text,
    textSecondary: Colors.textSecondary,
  },
  
  button: {
    primary: {
      background: Colors.primary,
      text: Colors.white,
      border: Colors.primary,
    },
    secondary: {
      background: Colors.surface,
      text: Colors.text,
      border: Colors.border,
    },
    danger: {
      background: Colors.error,
      text: Colors.white,
      border: Colors.error,
    },
  },
  
  input: {
    background: Colors.surface,
    text: Colors.text,
    placeholder: Colors.textSecondary,
    border: Colors.border,
    borderFocus: Colors.primary,
    borderError: Colors.error,
  },
  
  navigation: {
    background: Colors.background,
    card: Colors.surface,
    text: Colors.text,
    border: Colors.border,
    tabActive: Colors.primary,
    tabInactive: Colors.textSecondary,
  },
} as const;

// Usage example:
// import { Colors, SemanticColors, Theme } from '@/styles/colors';
// 
// const styles = StyleSheet.create({
//   container: {
//     backgroundColor: Colors.background,
//   },
//   card: {
//     backgroundColor: Theme.card.background,
//     borderColor: Theme.card.border,
//   },
//   agentStatus: {
//     color: SemanticColors.agentActive,
//   },
// });
