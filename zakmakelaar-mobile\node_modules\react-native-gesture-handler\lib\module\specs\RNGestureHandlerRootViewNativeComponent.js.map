{"version": 3, "sources": ["RNGestureHandlerRootViewNativeComponent.ts"], "names": ["codegenNativeComponent"], "mappings": "AAAA,OAAOA,sBAAP,MAAmC,yDAAnC;AAKA,eAAeA,sBAAsB,CAAc,0BAAd,CAArC", "sourcesContent": ["import codegenNativeComponent from 'react-native/Libraries/Utilities/codegenNativeComponent';\nimport type { ViewProps } from 'react-native';\n\ninterface NativeProps extends ViewProps {}\n\nexport default codegenNativeComponent<NativeProps>('RNGestureHandlerRootView');\n"]}